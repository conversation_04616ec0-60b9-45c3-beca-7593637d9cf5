# Design Document

## Overview

The discount system will be implemented as a comprehensive solution that integrates with the existing Infinity Portal Frontend architecture. The system will provide flexible discount management capabilities through an admin interface and seamless discount application during the cart checkout process.

The design follows the existing architectural patterns in the codebase, utilizing the repository pattern for data persistence, service layer for business logic, and tRPC for type-safe API communication. The system is designed with extensibility in mind, allowing for easy addition of new discount types and storage providers in the future.

## Architecture

### Directory Structure

```
src/
├── app/                          # Frontend Next.js App Router
│   ├── admin/discounts/          # Admin UI for discount management (TO BE CREATED)
│   ├── components/CartDrawer.tsx # Updated with discount display (TO BE UPDATED)
│   └── api/trpc/[trpc]/route.ts  # tRPC API route handler (TO BE CREATED)
│
├── server/                       # Backend server code (IMPLEMENTED)
│   ├── features/                 # Feature-based organization
│   │   └── discount/             # Discount feature module (IMPLEMENTED)
│   │       ├── discount.router.ts      # tRPC router for discounts (TO BE CREATED)
│   │       ├── discount.service.ts     # Business logic (IMPLEMENTED)
│   │       ├── discount.engine.ts      # Calculation engine (IMPLEMENTED)
│   │       ├── discount.repository.ts  # Data access (IMPLEMENTED)
│   │       ├── discount.types.ts       # Feature-specific types (IMPLEMENTED)
│   │       ├── discount.validation.ts  # Zod schemas (IMPLEMENTED)
│   │       ├── discount.service.interface.ts # Service interface (IMPLEMENTED)
│   │       ├── discount.engine.interface.ts  # Engine interface (IMPLEMENTED)
│   │       ├── discount.errors.ts      # Custom error classes (IMPLEMENTED)
│   │       ├── discount.config.ts      # Configuration (IMPLEMENTED)
│   │       ├── discount.utils.ts       # Utility functions (IMPLEMENTED)
│   │       ├── discount.validation.helpers.ts # Validation helpers (IMPLEMENTED)
│   │       ├── calculators/            # Discount calculators (IMPLEMENTED)
│   │       │   └── percentage-cap.calculator.ts
│   │       ├── storage/                # Storage providers (IMPLEMENTED)
│   │       │   ├── in-memory.provider.ts
│   │       │   ├── storage.interface.ts
│   │       │   └── storage.errors.ts
│   │       └── __tests__/              # Comprehensive test suite (IMPLEMENTED)
│   │
│   ├── shared/                   # Shared server utilities (PARTIALLY IMPLEMENTED)
│   │   ├── trpc/                 # tRPC configuration (TO BE CREATED)
│   │   │   ├── trpc.ts           # tRPC setup and context (TO BE CREATED)
│   │   │   ├── router.ts         # Root router combining features (TO BE CREATED)
│   │   │   └── middleware.ts     # Auth and other middleware (TO BE CREATED)
│   │   ├── types/                # Shared types (IMPLEMENTED)
│   │   │   └── api.types.ts
│   │   └── utils/                # Shared utilities (IMPLEMENTED)
│   │       ├── errors.ts         # Custom error classes
│   │       └── common.ts         # Common utilities
│   │
│   └── index.ts                  # Server exports (IMPLEMENTED)
│
└── lib/                          # Frontend-server shared utilities
    └── trpc-client.ts            # Frontend tRPC client setup (TO BE CREATED)
```

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer (src/app/)"
        A[Admin Discount Management UI]
        B[Cart Drawer with Discount Display]
        C[Header Cart Button with Pricing]
        D[tRPC Client]
    end
    
    subgraph "API Layer (src/server/api/)"
        E[tRPC Router]
        F[Discount Procedures]
        G[API Route Handler]
    end
    
    subgraph "Service Layer (src/server/services/)"
        H[Discount Service]
        I[Discount Engine]
    end
    
    subgraph "Repository Layer (src/server/repositories/)"
        J[Discount Repository]
        K[Storage Interface]
    end
    
    subgraph "Storage Layer (src/server/storage/)"
        L[In-Memory Provider]
        M[Future: Database Provider]
    end
    
    A --> D
    B --> D
    C --> D
    D --> G
    G --> E
    E --> F
    F --> H
    H --> I
    H --> J
    J --> K
    K --> L
    K --> M
```

### Component Interaction Flow

```mermaid
sequenceDiagram
    participant UI as Cart Drawer
    participant API as tRPC API
    participant Service as Discount Service
    participant Engine as Discount Engine
    participant Repo as Discount Repository
    participant Storage as Storage Provider
    
    UI->>API: Calculate discounts for cart
    API->>Service: processCartDiscounts(cartItems)
    Service->>Repo: getActiveDiscounts()
    Repo->>Storage: findAll(active=true)
    Storage-->>Repo: discount rules
    Repo-->>Service: active discounts
    Service->>Engine: calculateDiscounts(cart, rules)
    Engine-->>Service: discount results
    Service-->>API: calculated discounts
    API-->>UI: discount response
    UI->>UI: Update cart display with discounts
```

## Components and Interfaces

### 1. Data Models and Types

#### Core Discount Types

```typescript
// Base discount interface
interface BaseDiscount {
  id: string;
  name: string;
  description?: string;
  type: DiscountType;
  isActive: boolean;
  validFrom: Date;
  validTo: Date;
  createdAt: Date;
  updatedAt: Date;
  usageCount: number;
  maxUsage?: number;
}

// Specific discount type for initial implementation
interface PercentageCapDiscount extends BaseDiscount {
  type: 'PERCENTAGE_CAP';
  percentage: number; // X% off
  maxDiscountAmount: number; // up to Y
  minCartValue: number; // on cart value above Z
}

// Union type for all discount types (extensible)
type Discount = PercentageCapDiscount;

// Discount calculation result
interface DiscountCalculationResult {
  totalDiscount: number;
  appliedDiscounts: AppliedDiscount[];
  originalTotal: number;
  finalTotal: number;
  savings: number;
}

interface AppliedDiscount {
  discountId: string;
  discountName: string;
  discountAmount: number;
  discountType: DiscountType;
}

// Server-side cart interfaces (compatible with frontend types)
interface ServerCartItem {
  skuId: number;
  variantSkuId?: number;
  quantity: number;
  pricePerUnit: number;
  mrpPerUnit: number;
}

// Extended cart item for discount calculations
interface CartItemWithDetails extends ServerCartItem {
  name?: string; // Optional for logging/debugging
  categoryId?: number; // For future category-based discounts
}
```

#### Zod Schemas for Type-Safe Validation

Zod is used extensively throughout the system for runtime type validation and seamless tRPC integration:

```typescript
// Core discount schemas (src/server/features/discount/discount.validation.ts)
import { z } from 'zod';

// Base discount schema
export const BaseDiscountSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
  validFrom: z.coerce.date(),
  validTo: z.coerce.date(),
  maxUsage: z.number().min(1).optional()
});

// Percentage cap discount schema
export const PercentageCapDiscountSchema = BaseDiscountSchema.extend({
  type: z.literal('PERCENTAGE_CAP'),
  percentage: z.number().min(0.01, 'Percentage must be greater than 0').max(100, 'Percentage cannot exceed 100'),
  maxDiscountAmount: z.number().min(0, 'Max discount amount cannot be negative'),
  minCartValue: z.number().min(0, 'Minimum cart value cannot be negative')
});

// Union schema for all discount types (extensible)
export const CreateDiscountSchema = z.discriminatedUnion('type', [
  PercentageCapDiscountSchema
]);

// Update schema (partial with required ID)
export const UpdateDiscountSchema = CreateDiscountSchema.partial().extend({
  id: z.string().uuid('Invalid discount ID format')
});

// Cart calculation schema - using existing DetailedCartItem structure
export const CalculateDiscountSchema = z.object({
  cartItems: z.array(z.object({
    skuId: z.number().int().positive('SKU ID must be a positive integer'),
    variantSkuId: z.number().int().positive().optional(),
    quantity: z.number().int().min(1, 'Quantity must be at least 1'),
    pricePerUnit: z.number().min(0, 'Price cannot be negative'),
    mrpPerUnit: z.number().min(0, 'MRP cannot be negative')
  })).min(1, 'Cart must contain at least one item')
});

// Query schemas
export const GetDiscountByIdSchema = z.object({
  id: z.string().uuid('Invalid discount ID format')
});

export const ListDiscountsSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  filters: z.object({
    isActive: z.boolean().optional(),
    type: z.enum(['PERCENTAGE_CAP']).optional(),
    validAt: z.coerce.date().optional()
  }).optional()
});

// Response schemas for type inference
export const DiscountResponseSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  description: z.string().optional(),
  type: z.enum(['PERCENTAGE_CAP']),
  isActive: z.boolean(),
  validFrom: z.date(),
  validTo: z.date(),
  createdAt: z.date(),
  updatedAt: z.date(),
  usageCount: z.number(),
  maxUsage: z.number().optional(),
  // Type-specific fields
  percentage: z.number().optional(),
  maxDiscountAmount: z.number().optional(),
  minCartValue: z.number().optional()
});

export const DiscountCalculationResultSchema = z.object({
  totalDiscount: z.number(),
  appliedDiscounts: z.array(z.object({
    discountId: z.string().uuid(),
    discountName: z.string(),
    discountAmount: z.number(),
    discountType: z.enum(['PERCENTAGE_CAP'])
  })),
  originalTotal: z.number(),
  finalTotal: z.number(),
  savings: z.number()
});

// Type inference from schemas (used throughout the application)
export type CreateDiscountInput = z.infer<typeof CreateDiscountSchema>;
export type UpdateDiscountInput = z.infer<typeof UpdateDiscountSchema>;
export type CalculateDiscountInput = z.infer<typeof CalculateDiscountSchema>;
export type DiscountResponse = z.infer<typeof DiscountResponseSchema>;
export type DiscountCalculationResult = z.infer<typeof DiscountCalculationResultSchema>;
```

### 2. Storage Provider Interface

```typescript
interface DiscountStorageProvider {
  // CRUD operations
  create(discount: Omit<Discount, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>): Promise<Discount>;
  findById(id: string): Promise<Discount | null>;
  findAll(filters?: DiscountFilters): Promise<Discount[]>;
  update(id: string, updates: Partial<Discount>): Promise<Discount>;
  delete(id: string): Promise<boolean>;
  
  // Usage tracking
  incrementUsage(id: string): Promise<void>;
  
  // Bulk operations
  findActiveDiscounts(): Promise<Discount[]>;
}

interface DiscountFilters {
  isActive?: boolean;
  type?: DiscountType;
  validAt?: Date;
}
```

### 3. Discount Engine Interface

```typescript
// Server-side discount engine interfaces (no frontend dependencies)
interface DiscountEngine {
  calculateDiscounts(
    cartItems: CartItemWithDetails[],
    availableDiscounts: Discount[]
  ): Promise<DiscountCalculationResult>;
  
  // Method to register new discount calculators
  registerCalculator(type: DiscountType, calculator: DiscountCalculator): void;
}

interface DiscountCalculator {
  canApply(discount: Discount, cartItems: CartItemWithDetails[]): boolean;
  calculate(discount: Discount, cartItems: CartItemWithDetails[]): number;
}
```

### 4. tRPC Router Definition

```typescript
const discountRouter = router({
  // Admin CRUD operations
  create: adminProcedure
    .input(CreateDiscountSchema)
    .mutation(async ({ input }) => { /* implementation */ }),
    
  list: adminProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      filters: z.object({
        isActive: z.boolean().optional(),
        type: z.string().optional()
      }).optional()
    }))
    .query(async ({ input }) => { /* implementation */ }),
    
  getById: adminProcedure
    .input(z.object({ id: z.string().uuid() }))
    .query(async ({ input }) => { /* implementation */ }),
    
  update: adminProcedure
    .input(UpdateDiscountSchema)
    .mutation(async ({ input }) => { /* implementation */ }),
    
  delete: adminProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ input }) => { /* implementation */ }),
    
  // Cart discount calculation
  calculate: publicProcedure
    .input(CalculateDiscountSchema)
    .query(async ({ input }) => { /* implementation */ }),
    
  // Analytics
  getUsageStats: adminProcedure
    .input(z.object({
      discountId: z.string().uuid().optional(),
      dateFrom: z.date().optional(),
      dateTo: z.date().optional()
    }))
    .query(async ({ input }) => { /* implementation */ })
});
```

## Data Models

### In-Memory Storage Structure

```typescript
interface InMemoryDiscountStore {
  discounts: Map<string, Discount>;
  usageLog: DiscountUsageEntry[];
}

interface DiscountUsageEntry {
  discountId: string;
  cartId: string;
  discountAmount: number;
  cartTotal: number;
  appliedAt: Date;
}
```

## Error Handling

### Error Types and Handling Strategy

```typescript
// Custom error types
class DiscountError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'DiscountError';
  }
}

class DiscountNotFoundError extends DiscountError {
  constructor(id: string) {
    super(`Discount with id ${id} not found`, 'DISCOUNT_NOT_FOUND');
  }
}

class DiscountCalculationError extends DiscountError {
  constructor(message: string) {
    super(message, 'CALCULATION_ERROR');
  }
}

// Error handling in tRPC procedures
const handleDiscountError = (error: unknown) => {
  if (error instanceof DiscountError) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: error.message,
      cause: error
    });
  }
  
  // Log unexpected errors to PostHog
  logger.error('Unexpected discount error:', error);
  
  throw new TRPCError({
    code: 'INTERNAL_SERVER_ERROR',
    message: 'An unexpected error occurred'
  });
};
```

### Graceful Degradation Strategy

```typescript
// Discount calculation with fallback
const calculateDiscountsWithFallback = async (
  cartItems: CartItemWithDetails[]
): Promise<DiscountCalculationResult> => {
  try {
    return await discountService.calculateDiscounts(cartItems);
  } catch (error) {
    // Log error to PostHog for monitoring
    logger.error('Discount calculation failed, using fallback', { error });
    
    // Return no discount result to allow checkout to proceed
    const originalTotal = cartItems.reduce(
      (sum, item) => sum + (item.pricePerUnit * item.quantity), 
      0
    );
    
    return {
      totalDiscount: 0,
      appliedDiscounts: [],
      originalTotal,
      finalTotal: originalTotal,
      savings: 0
    };
  }
};
```

## Testing Strategy

### Unit Testing Approach

```typescript
// Discount engine tests
describe('DiscountEngine', () => {
  describe('PercentageCapCalculator', () => {
    it('should apply percentage discount when cart meets minimum value', () => {
      // Test implementation
    });
    
    it('should cap discount at maximum amount', () => {
      // Test implementation
    });
    
    it('should not apply discount when cart is below minimum value', () => {
      // Test implementation
    });
  });
});

// Storage provider tests
describe('InMemoryDiscountStorage', () => {
  it('should create and retrieve discounts', async () => {
    // Test implementation
  });
  
  it('should handle concurrent access safely', async () => {
    // Test implementation
  });
});

// tRPC procedure tests
describe('Discount tRPC Router', () => {
  it('should validate input schemas correctly', async () => {
    // Test implementation
  });
  
  it('should handle errors gracefully', async () => {
    // Test implementation
  });
});
```



## Implementation Phases

### Phase 1: Core Infrastructure
1. Set up tRPC router and procedures
2. Implement in-memory storage provider
3. Create discount engine with percentage cap calculator
4. Add Zod validation schemas
5. Implement error handling and logging

### Phase 2: Admin Interface
1. Create discount management pages in admin portal
2. Implement CRUD operations for discounts
3. Add form validation and error handling
4. Create discount list with filtering and pagination
5. Add usage analytics dashboard

### Phase 3: Cart Integration
1. Integrate discount calculation into cart service
2. Update CartDrawer component to display discounts
3. Update header cart button with discount pricing
4. Add loading states and error handling
5. Implement graceful degradation

### Phase 4: Testing and Documentation
1. Comprehensive unit testing for discount engine and storage
2. Error monitoring and logging setup with PostHog integration
3. Code documentation and inline comments
4. System validation and requirement verification

### Phase 5: Future Enhancements (Out of Current Scope)
1. Additional discount types (fixed amount, BOGO, category-based, etc.)
2. Database storage provider to replace in-memory storage
3. Advanced analytics dashboard with charts and trends
4. Discount scheduling and automated activation/deactivation
5. Multi-tier discount combinations and stacking rules
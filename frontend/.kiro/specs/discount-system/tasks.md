# Implementation Plan

- [x] 1. Set up project structure and core interfaces
  - Create directory structure for server-side discount feature
  - Define interfaces that establish system boundaries
  - _Requirements: 1.1_

- [x] 2. Implement data models and validation
- [x] 2.1 Create core data model interfaces and types
  - Write TypeScript interfaces for all data models
  - Implement validation functions for data integrity
  - _Requirements: 2.1, 3.3, 1.2_

- [x] 2.2 Implement Zod validation schemas
  - Write comprehensive Zod schemas for all discount operations
  - Create type inference exports for frontend consumption
  - Add input validation with detailed error messages
  - _Requirements: 5.7, 5.8, 6.3_

- [x] 3. Create storage mechanism
- [x] 3.1 Implement storage provider interface
  - Write storage provider interface with CRUD operations
  - Add usage tracking and filtering capabilities
  - _Requirements: 4.2, 4.3, 4.4, 4.5_

- [x] 3.2 Build in-memory storage provider
  - Implement in-memory storage using Map-based storage
  - Add thread-safe operations for concurrent access
  - Implement filtering and active discount queries
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 3.3 Test in-memory storage provider
  - Write unit tests for in-memory storage provider operations
  - Test CRUD operations and filtering functionality
  - Test concurrent access scenarios for thread safety
  - _Requirements: 6.1, 6.2, 6.5_

- [x] 4. Build discount calculation engine
- [x] 4.1 Create discount engine interface 
  - Implement discount engine with pluggable calculators
  - Add calculator registration system for extensibility
  - Write unit tests for discount engine interface and calculator registration
  - _Requirements: 3.7, 3.8, 6.1, 6.2, 6.3, 6.4_

- [x] 4.2 Implement percentage cap discount calculator
  - Create calculator for "X% off up to Y on cart value above Z"
  - Add validation for minimum cart value threshold
  - Implement discount cap logic
  - Write comprehensive unit tests for percentage cap calculator logic
  - Test discount engine with various cart scenarios and edge cases
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 6.1, 6.2, 6.3, 6.4_

- [x] 5. Create discount repository layer
  - Implement discount repository using storage provider interface
  - Add repository methods for CRUD operations
  - Implement active discount filtering and usage tracking
  - Add error handling for storage operations
  - Write unit tests for discount repository CRUD operations
  - Test error handling and edge cases in repository layer
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5, 6.1, 6.2, 6.6_

- [x] 6. Implement discount service layer
  - Create discount service with business logic
  - Integrate repository and calculation engine
  - Add discount validation and application logic
  - Implement usage tracking and analytics data collection
  - Write unit tests for discount service business logic
  - Test service integration with repository and calculation engine
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 8.6, 6.1, 6.2, 6.6_

- [x] 7. Set up tRPC infrastructure
- [x] 7.1 Install and configure tRPC dependencies
  - Install @trpc/server, @trpc/client, @trpc/react-query, and @trpc/next packages
  - Update package.json with required tRPC dependencies
  - _Requirements: 5.8, 5.9_

- [x] 7.2 Create tRPC configuration and context
  - Set up tRPC with context and middleware in src/server/shared/trpc/
  - Configure authentication middleware for admin procedures
  - Create base procedures (publicProcedure, adminProcedure)
  - _Requirements: 5.8, 5.9_

- [x] 7.3 Create root router and API route handler
  - Implement root router that combines feature routers
  - Set up API route handler for tRPC in Next.js App Router at src/app/api/trpc/[trpc]/route.ts
  - _Requirements: 5.1, 5.8, 5.9_

- [x] 7.4 Set up frontend tRPC client
  - Create tRPC client setup in src/lib/trpc-client.ts
  - Configure client with proper error handling and React Query integration
  - Add type-safe client exports for frontend consumption
  - Set up client-side error handling and retry logic
  - _Requirements: 5.6, 5.7, 5.8, 5.9_

- [ ] 8. Implement tRPC discount procedures
- [x] 8.1 Create CRUD procedures for discount management
  - Implement create discount procedure with validation
  - Add list discounts procedure with pagination and filtering
  - Create get discount by ID procedure
  - Implement update discount procedure
  - Add delete discount procedure
  - Write unit tests for all discount CRUD tRPC procedures
  - Test input validation and error handling in procedures
  - Test authentication and authorization for admin procedures
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5, 5.6, 5.7, 5.8, 5.9, 6.1, 6.2, 6.3, 6.6_

- [x] 8.2 Implement discount calculation procedure
  - Create calculate discounts procedure for cart items
  - Add graceful error handling with fallback to no discount
  - Implement PostHog error logging for monitoring
  - Add input validation for cart items
  - Write unit tests for discount calculation procedure
  - Test error scenarios and graceful degradation
  - _Requirements: 5.6, 7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7, 7.8, 6.1, 6.2, 6.3, 6.6_

- [ ] 9. Create admin discount management interface
- [x] 9.1 Build discount list page
  - Create discount list page with data table
  - Add pagination and filtering controls
  - Implement status indicators and usage statistics display
  - _Requirements: 1.1, 1.2, 8.1, 8.2_

- [x] 9.2 Implement discount creation form
  - Build create discount form with validation
  - Add form fields for percentage, cap, and minimum cart value
  - Implement date pickers for validity period
  - Add real-time validation feedback
  - _Requirements: 1.3, 1.4, 1.5_

- [x] 9.3 Add discount editing functionality
  - Create edit discount form with pre-populated data
  - Implement update functionality with validation
  - Add confirmation dialogs for destructive actions
  - _Requirements: 1.6, 1.7_

- [x] 9.4 Implement discount deletion
  - Create delete discount confirmation modal
  - Implement soft delete or hard delete based on requirements
  - Add safety checks for active discounts
  - _Requirements: 1.8_

- [ ] 10. Integrate discount calculation into cart system
- [ ] 10.1 Modify cart service for discount integration
  - Update cart service to call discount calculation API
  - Add discount calculation to cart details computation
  - Implement loading states during discount calculation
  - Add error handling with graceful degradation
  - _Requirements: 2.1, 2.5, 7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7, 7.8_

- [ ] 10.2 Update CartDrawer component with discount display
  - Add discount information section to cart drawer
  - Display original price, discount amount, and final price
  - Show applied discount details and savings
  - Add loading indicators during discount calculation
  - Handle discount calculation errors gracefully
  - _Requirements: 2.1, 2.2, 2.4, 2.5, 2.6, 2.7_

- [ ] 10.3 Update header cart button with discount pricing
  - Modify header cart button to show discounted total
  - Add loading indicator during discount calculation
  - Ensure consistent pricing display between header and drawer
  - Handle empty cart state appropriately
  - _Requirements: 2.1, 2.3, 2.4, 2.5, 2.8_

- [ ] 11. Implement discount usage analytics
  - Add usage statistics tracking in discount service
  - Create analytics tRPC procedure for admin dashboard
  - Implement usage count increment on discount application
  - Add date range filtering for usage statistics
  - Create analytics display components for admin interface
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5, 8.6, 8.7_

- [ ] 12. Add comprehensive error handling and logging
  - Implement custom error classes for discount operations
  - Add tRPC error handling with proper error codes
  - Set up PostHog logging for discount calculation failures
  - Add server-side logging for debugging and monitoring
  - Test error scenarios and graceful degradation
  - _Requirements: 7.4, 7.5, 7.6, 7.7, 7.8_

- [ ] 13. Final integration and system validation
  - Verify all discount system components work together
  - Test complete discount creation, calculation, and display flow
  - Ensure all requirements are implemented correctly
  - Validate error handling and graceful degradation
  - Perform system-wide testing of discount functionality
  - _Requirements: All requirements verification_
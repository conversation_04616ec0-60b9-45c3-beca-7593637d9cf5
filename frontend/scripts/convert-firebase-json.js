#!/usr/bin/env node

/**
 * Firebase Service Account JSON Converter
 * 
 * This script helps convert a Firebase service account JSON file
 * into a properly formatted environment variable string.
 * 
 * Usage:
 *   node scripts/convert-firebase-json.js /path/to/service-account.json
 *   node scripts/convert-firebase-json.js --stdin < service-account.json
 */

const fs = require('fs');
const path = require('path');

function convertJsonToEnvString(jsonContent) {
  try {
    // Parse JSON to validate it
    const serviceAccount = JSON.parse(jsonContent);
    
    // Validate required fields
    const requiredFields = ['type', 'project_id', 'private_key', 'client_email'];
    const missingFields = requiredFields.filter(field => !serviceAccount[field]);
    
    if (missingFields.length > 0) {
      console.error('❌ Missing required fields:', missingFields.join(', '));
      process.exit(1);
    }
    
    // Convert to single-line string
    const envString = JSON.stringify(serviceAccount);
    
    return envString;
  } catch (error) {
    console.error('❌ Invalid JSON:', error.message);
    process.exit(1);
  }
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🔥 Firebase Service Account JSON Converter

Usage:
  node scripts/convert-firebase-json.js /path/to/service-account.json
  node scripts/convert-firebase-json.js --stdin < service-account.json

This script converts a Firebase service account JSON file into a properly
formatted string that can be used as the FIREBASE_SERVICE_ACCOUNT_JSON
environment variable.
`);
    process.exit(0);
  }
  
  let jsonContent;
  
  if (args[0] === '--stdin') {
    // Read from stdin
    console.log('📖 Reading from stdin...');
    
    let stdinData = '';
    process.stdin.setEncoding('utf8');
    
    process.stdin.on('data', (chunk) => {
      stdinData += chunk;
    });
    
    process.stdin.on('end', () => {
      jsonContent = stdinData.trim();
      processJson(jsonContent);
    });
    
    return;
  }
  
  // Read from file
  const filePath = args[0];
  
  if (!fs.existsSync(filePath)) {
    console.error(`❌ File not found: ${filePath}`);
    process.exit(1);
  }
  
  console.log(`📖 Reading from file: ${filePath}`);
  
  try {
    jsonContent = fs.readFileSync(filePath, 'utf8');
    processJson(jsonContent);
  } catch (error) {
    console.error(`❌ Error reading file: ${error.message}`);
    process.exit(1);
  }
}

function processJson(jsonContent) {
  console.log('🔄 Converting JSON to environment variable format...');
  
  const envString = convertJsonToEnvString(jsonContent);
  
  console.log('\n✅ Conversion successful!\n');
  console.log('📋 Copy this to your .env file:\n');
  console.log(`FIREBASE_SERVICE_ACCOUNT_JSON='${envString}'`);
  console.log('\n📝 Or export it directly:\n');
  console.log(`export FIREBASE_SERVICE_ACCOUNT_JSON='${envString}'`);
  
  // Validate the converted string by parsing it back
  try {
    const parsed = JSON.parse(envString);
    console.log('\n✅ Validation successful!');
    console.log(`   Project ID: ${parsed.project_id}`);
    console.log(`   Client Email: ${parsed.client_email}`);
    console.log(`   Private Key: ${parsed.private_key ? '✓ Present' : '✗ Missing'}`);
  } catch (error) {
    console.error('\n❌ Validation failed:', error.message);
    process.exit(1);
  }
}

// Handle process termination gracefully
process.on('SIGINT', () => {
  console.log('\n👋 Conversion cancelled');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 Conversion terminated');
  process.exit(0);
});

// Run the script
main();

/**
 * Root tRPC Router
 * 
 * This file combines all feature routers into a single root router
 * that serves as the main API entry point.
 */

import { router } from './trpc';
import { discountRouter } from '../../features/discount/discount.router';

/**
 * Root router combining all feature routers
 */
export const appRouter = router({
  discount: discountRouter,
  // Future feature routers can be added here
  // sku: skuRouter,
  // category: categoryRouter,
  // order: orderRouter,
});

export type AppRouter = typeof appRouter;
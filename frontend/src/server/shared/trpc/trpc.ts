/**
 * tRPC configuration and context setup
 * 
 * This file sets up the core tRPC configuration, context, and base procedures
 * for the discount system and other features.
 */

import { initTRPC, TRPCError } from '@trpc/server';
import superjson from 'superjson';
import { ZodError } from 'zod';

/**
 * Context type for tRPC
 * This context is available to all procedures
 */
export interface Context {
  req: Request;
  userId?: string;
  userRole?: string;
  isAdmin: boolean;
}

/**
 * Initialize tRPC with context and transformer
 */
const t = initTRPC.context<Context>().create({
  transformer: superjson,
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    };
  },
});

/**
 * Create a server-side caller
 */
export const createCallerFactory = t.createCallerFactory;

/**
 * Router and procedure helpers
 */
export const router = t.router;
export const middleware = t.middleware;

/**
 * Public procedure - no authentication required
 */
export const publicProcedure = t.procedure;

/**
 * Authentication middleware
 * Ensures user is authenticated
 */
const enforceUserIsAuthed = middleware(async ({ ctx, next }) => {
  if (!ctx.userId) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Authentication required',
    });
  }

  return next({
    ctx: {
      ...ctx,
      userId: ctx.userId,
    },
  });
});

/**
 * Admin authentication middleware
 * Ensures user is authenticated and has admin privileges
 */
const enforceUserIsAdmin = middleware(async ({ ctx, next }) => {
  if (!ctx.userId) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Authentication required',
    });
  }

  if (!ctx.isAdmin) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Admin privileges required',
    });
  }

  return next({
    ctx: {
      ...ctx,
      userId: ctx.userId,
      isAdmin: ctx.isAdmin,
    },
  });
});

/**
 * Protected procedure - requires authentication
 */
export const protectedProcedure = t.procedure.use(enforceUserIsAuthed);

/**
 * Admin procedure - requires admin authentication
 */
export const adminProcedure = t.procedure.use(enforceUserIsAdmin);

/**
 * Logging middleware for debugging
 */
export const loggingMiddleware = middleware(async ({ path, type, next }) => {
  const start = Date.now();
  const result = await next();
  const durationMs = Date.now() - start;
  
  // eslint-disable-next-line no-console
  console.log(`${type} ${path} - ${durationMs}ms`);
  
  return result;
});

/**
 * Rate limiting middleware (basic implementation)
 */
const requestCounts = new Map<string, { count: number; resetTime: number }>();

export const rateLimitMiddleware = (maxRequests = 100, windowMs = 60000) =>
  middleware(async ({ ctx, next }) => {
    // Use userId or a generic identifier for rate limiting
    const identifier = ctx.userId || 'anonymous';
    const now = Date.now();
    
    const userRequests = requestCounts.get(identifier);
    
    if (!userRequests || now > userRequests.resetTime) {
      requestCounts.set(identifier, { count: 1, resetTime: now + windowMs });
    } else {
      userRequests.count++;
      
      if (userRequests.count > maxRequests) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Rate limit exceeded',
        });
      }
    }
    
    return next();
  });

/**
 * Error handling utilities
 */
export const handleTRPCError = (error: unknown, context?: string) => {
  // eslint-disable-next-line no-console
  console.error(`tRPC Error${context ? ` in ${context}` : ''}:`, error);
  
  if (error instanceof TRPCError) {
    throw error;
  }
  
  if (error instanceof ZodError) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'Validation error',
      cause: error,
    });
  }
  
  throw new TRPCError({
    code: 'INTERNAL_SERVER_ERROR',
    message: 'An unexpected error occurred',
  });
};
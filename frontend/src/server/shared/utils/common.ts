/**
 * Shared utility functions for server-side operations
 * 
 * This file contains generic, reusable utility functions that can be used
 * across multiple features and domains. Feature-specific utilities should be
 * defined within their respective feature directories.
 */

import type { PaginationParams } from '../types/api.types';

/**
 * Data transformation utilities
 */
export const DataUtils = {
  /**
   * Create a deep clone of an object using JSON serialization
   * Note: This method has limitations (no functions, undefined values, etc.)
   * but is sufficient for plain data objects
   */
  deepClone: <T>(obj: T): T => {
    return JSON.parse(JSON.stringify(obj));
  },

  /**
   * Sanitize string input by trimming and normalizing whitespace
   */
  sanitizeString: (str: string): string => {
    return str.trim().replace(/\s+/g, ' ');
  },

  /**
   * Normalize whitespace in a string (alias for sanitizeString for backward compatibility)
   */
  normalizeWhitespace: (str: string): string => {
    return str.replace(/\s+/g, ' ').trim();
  },

  /**
   * Round number to specified decimal places
   */
  roundToDecimals: (num: number, decimals = 2): number => {
    const factor = Math.pow(10, decimals);
    return Math.round(num * factor) / factor;
  },

  /**
   * Check if a value is a valid number
   */
  isValidNumber: (value: unknown): value is number => {
    return typeof value === 'number' && !isNaN(value) && isFinite(value);
  },

  /**
   * Safely convert a value to a number
   */
  toNumber: (value: unknown, defaultValue = 0): number => {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? defaultValue : parsed;
    }
    return defaultValue;
  }
};

/**
 * Validation utilities
 */
export const ValidationUtils = {
  /**
   * Validate pagination parameters
   */
  validatePagination: (pagination?: PaginationParams): string[] => {
    const errors: string[] = [];

    if (pagination) {
      if (pagination.page < 1) {
        errors.push('Page must be at least 1');
      }

      if (pagination.limit < 1) {
        errors.push('Limit must be at least 1');
      }

      if (pagination.limit > 100) {
        errors.push('Limit cannot exceed 100');
      }
    }

    return errors;
  },

  /**
   * Check if a string is not empty after trimming
   */
  isNonEmptyString: (value: unknown): value is string => {
    return typeof value === 'string' && value.trim().length > 0;
  },

  /**
   * Check if a value is a valid email format
   */
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Check if a value is a valid date
   */
  isValidDate: (date: unknown): date is Date => {
    return date instanceof Date && !isNaN(date.getTime());
  }
};

/**
 * Math utilities
 */
export const MathUtils = {
  /**
   * Calculate total pages for pagination
   */
  calculateTotalPages: (total: number, limit: number): number => {
    return Math.ceil(total / limit);
  },

  /**
   * Clamp a number between min and max values
   */
  clamp: (value: number, min: number, max: number): number => {
    return Math.min(Math.max(value, min), max);
  },

  /**
   * Calculate percentage with optional decimal places
   */
  calculatePercentage: (value: number, total: number, decimals = 2): number => {
    if (total === 0) return 0;
    return DataUtils.roundToDecimals((value / total) * 100, decimals);
  }
};

/**
 * Array utilities
 */
export const ArrayUtils = {
  /**
   * Remove duplicates from an array based on a key function
   */
  uniqueBy: <T, K>(array: T[], keyFn: (item: T) => K): T[] => {
    const seen = new Set<K>();
    return array.filter(item => {
      const key = keyFn(item);
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  },

  /**
   * Group array items by a key function
   */
  groupBy: <T, K extends string | number | symbol>(
    array: T[], 
    keyFn: (item: T) => K
  ): Record<K, T[]> => {
    return array.reduce((groups, item) => {
      const key = keyFn(item);
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(item);
      return groups;
    }, {} as Record<K, T[]>);
  },

  /**
   * Check if an array is not empty
   */
  isNonEmpty: <T>(array: T[]): array is [T, ...T[]] => {
    return array.length > 0;
  }
};

/**
 * Object utilities
 */
export const ObjectUtils = {
  /**
   * Pick specific properties from an object
   */
  pick: <T extends object, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> => {
    const result = {} as Pick<T, K>;
    keys.forEach(key => {
      if (key in obj) {
        result[key] = obj[key];
      }
    });
    return result;
  },

  /**
   * Omit specific properties from an object
   */
  omit: <T extends object, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> => {
    const result = { ...obj };
    keys.forEach(key => {
      delete result[key];
    });
    return result;
  },

  /**
   * Check if an object has any own properties
   */
  isEmpty: (obj: object): boolean => {
    return Object.keys(obj).length === 0;
  }
};

/**
 * Date utilities
 */
export const DateUtils = {
  /**
   * Check if a date is in the past
   */
  isPast: (date: Date): boolean => {
    return date.getTime() < Date.now();
  },

  /**
   * Check if a date is in the future
   */
  isFuture: (date: Date): boolean => {
    return date.getTime() > Date.now();
  },

  /**
   * Format a date to ISO string
   */
  toISOString: (date: Date): string => {
    return date.toISOString();
  },

  /**
   * Create a date from ISO string with validation
   */
  fromISOString: (isoString: string): Date | null => {
    try {
      const date = new Date(isoString);
      return ValidationUtils.isValidDate(date) ? date : null;
    } catch {
      return null;
    }
  }
};

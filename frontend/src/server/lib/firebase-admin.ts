/**
 * Firebase Admin SDK Configuration
 *
 * Server-side only Firebase Admin SDK initialization with proper error handling
 * and connection validation. Uses FIREBASE_SERVICE_ACCOUNT_JSON environment variable.
 */

import { initializeApp, getApps, type App } from 'firebase-admin/app';
import * as admin from 'firebase-admin';
import { getFirestore, type Firestore } from 'firebase-admin/firestore';
import { logger } from '../../lib/logger';

// Service account interface (matches Firebase service account JSON structure)
interface ServiceAccountConfig {
  type: string;
  project_id: string;
  private_key_id: string;
  private_key: string;
  client_email: string;
  client_id: string;
  auth_uri: string;
  token_uri: string;
  auth_provider_x509_cert_url: string;
  client_x509_cert_url: string;
  universe_domain: string;
}

// Firebase Admin SDK instance
let firebaseApp: App | null = null;
let firestoreDb: Firestore | null = null;

/**
 * Load Firebase service account configuration from FIREBASE_SERVICE_ACCOUNT_JSON environment variable
 */
function loadFirebaseConfig(): ServiceAccountConfig {
  const serviceAccountJson = process.env.FIREBASE_SERVICE_ACCOUNT_JSON;

  if (!serviceAccountJson) {
    throw new Error('FIREBASE_SERVICE_ACCOUNT_JSON environment variable is required');
  }

  try {
    logger.info('[Firebase] Loading configuration from FIREBASE_SERVICE_ACCOUNT_JSON');

    const serviceAccount: ServiceAccountConfig = JSON.parse(serviceAccountJson);

    // Validate required fields
    if (!serviceAccount.project_id) {
      throw new Error('project_id is missing from service account JSON');
    }

    if (!serviceAccount.client_email) {
      throw new Error('client_email is missing from service account JSON');
    }

    if (!serviceAccount.private_key) {
      throw new Error('private_key is missing from service account JSON');
    }

    // Handle private key formatting (ensure proper newlines)
    serviceAccount.private_key = serviceAccount.private_key.replace(/\\n/g, '\n');

    return serviceAccount;

  } catch (error) {
    logger.error('[Firebase] Failed to parse FIREBASE_SERVICE_ACCOUNT_JSON:', error);
    throw new Error(`Invalid FIREBASE_SERVICE_ACCOUNT_JSON: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Initialize Firebase Admin SDK
 */
export async function initializeFirebaseAdmin(): Promise<App> {
  try {
    // Check if Firebase is already initialized
    const existingApps = getApps();
    if (existingApps.length > 0) {
      logger.info('[Firebase] Using existing Firebase Admin SDK instance');
      firebaseApp = existingApps[0];
      return firebaseApp;
    }

    // Load configuration
    const serviceAccount = loadFirebaseConfig();

    logger.info('[Firebase] Initializing Firebase Admin SDK...', {
      projectId: serviceAccount.project_id,
      clientEmail: serviceAccount.client_email
    });

    // Initialize Firebase Admin SDK with service account credentials
    firebaseApp = initializeApp({
      credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
      databaseURL: process.env.FIREBASE_DATABASE_URL || `https://${serviceAccount.project_id}-default-rtdb.firebaseio.com`
    });

    logger.info('[Firebase] Firebase Admin SDK initialized successfully');
    return firebaseApp;

  } catch (error) {
    logger.error('[Firebase] Failed to initialize Firebase Admin SDK:', error);
    throw new Error(`Firebase initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get Firestore database instance
 */
export async function getFirestoreDb(): Promise<Firestore> {
  try {
    if (!firestoreDb) {
      // Ensure Firebase is initialized
      if (!firebaseApp) {
        await initializeFirebaseAdmin();
      }

      if (!firebaseApp) {
        throw new Error('Firebase app not initialized');
      }

      // Initialize Firestore
      firestoreDb = getFirestore(firebaseApp);
      
      logger.info('[Firebase] Firestore database instance created');
    }

    return firestoreDb;
  } catch (error) {
    logger.error('[Firebase] Failed to get Firestore database:', error);
    throw new Error(`Firestore initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Validate Firebase connection
 */
export async function validateFirebaseConnection(): Promise<boolean> {
  try {
    logger.info('[Firebase] Validating Firebase connection...');

    const db = await getFirestoreDb();
    
    // Test connection by attempting to read from a test collection
    const testCollection = db.collection('_connection_test');
    const testDoc = testCollection.doc('test');
    
    // Try to read the document (this will test the connection)
    await testDoc.get();
    
    logger.info('[Firebase] Firebase connection validated successfully');
    return true;

  } catch (error) {
    logger.error('[Firebase] Firebase connection validation failed:', error);
    return false;
  }
}

/**
 * Get Firebase app instance (for advanced usage)
 */
export function getFirebaseApp(): App | null {
  return firebaseApp;
}

/**
 * Reset Firebase instances (useful for testing)
 */
export function resetFirebaseInstances(): void {
  firebaseApp = null;
  firestoreDb = null;
  logger.debug('[Firebase] Firebase instances reset');
}



/**
 * Firebase health check
 */
export async function firebaseHealthCheck(): Promise<{
  status: 'healthy' | 'unhealthy';
  details: Record<string, unknown>;
}> {
  try {
    const isConnected = await validateFirebaseConnection();

    // Get project ID from current config
    let projectId = 'not-configured';
    try {
      const config = loadFirebaseConfig();
      projectId = config.project_id;
    } catch {
      // Config loading failed, keep default
    }

    return {
      status: isConnected ? 'healthy' : 'unhealthy',
      details: {
        initialized: firebaseApp !== null,
        firestoreReady: firestoreDb !== null,
        connectionValid: isConnected,
        projectId
      }
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        initialized: false,
        firestoreReady: false,
        connectionValid: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

/**
 * Firebase Admin SDK Configuration
 * 
 * Server-side only Firebase Admin SDK initialization with proper error handling
 * and connection validation. Uses environment variables for configuration.
 */

import { initializeApp, getApps, cert, type App } from 'firebase-admin/app';
import { getFirestore, type Firestore } from 'firebase-admin/firestore';
import { logger } from '../../lib/logger';

// Firebase configuration interface
interface FirebaseConfig {
  projectId: string;
  clientEmail: string;
  privateKey: string;
  databaseURL?: string;
}

// Service account interface (matches Firebase service account JSON structure)
interface ServiceAccountConfig {
  type: string;
  project_id: string;
  private_key_id: string;
  private_key: string;
  client_email: string;
  client_id: string;
  auth_uri: string;
  token_uri: string;
  auth_provider_x509_cert_url: string;
  client_x509_cert_url: string;
  universe_domain: string;
}

// Firebase Admin SDK instance
let firebaseApp: App | null = null;
let firestoreDb: Firestore | null = null;

/**
 * Load Firebase configuration from environment variables
 * Supports both single JSON string and individual environment variables
 */
function loadFirebaseConfig(): FirebaseConfig {
  // Try to load from single JSON string first
  const serviceAccountJson = process.env.FIREBASE_SERVICE_ACCOUNT_JSON;

  if (serviceAccountJson) {
    return loadConfigFromJson(serviceAccountJson);
  }

  // Fallback to individual environment variables
  return loadConfigFromIndividualVars();
}

/**
 * Load configuration from a single JSON string
 */
function loadConfigFromJson(serviceAccountJson: string): FirebaseConfig {
  try {
    logger.info('[Firebase] Loading configuration from FIREBASE_SERVICE_ACCOUNT_JSON');

    const serviceAccount: ServiceAccountConfig = JSON.parse(serviceAccountJson);

    // Validate required fields
    if (!serviceAccount.project_id) {
      throw new Error('project_id is missing from service account JSON');
    }

    if (!serviceAccount.client_email) {
      throw new Error('client_email is missing from service account JSON');
    }

    if (!serviceAccount.private_key) {
      throw new Error('private_key is missing from service account JSON');
    }

    // Handle private key formatting (ensure proper newlines)
    const formattedPrivateKey = serviceAccount.private_key.replace(/\\n/g, '\n');

    return {
      projectId: serviceAccount.project_id,
      clientEmail: serviceAccount.client_email,
      privateKey: formattedPrivateKey,
      databaseURL: process.env.FIREBASE_DATABASE_URL // Optional override
    };

  } catch (error) {
    logger.error('[Firebase] Failed to parse FIREBASE_SERVICE_ACCOUNT_JSON:', error);
    throw new Error(`Invalid FIREBASE_SERVICE_ACCOUNT_JSON: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Load configuration from individual environment variables (legacy approach)
 */
function loadConfigFromIndividualVars(): FirebaseConfig {
  logger.info('[Firebase] Loading configuration from individual environment variables');

  const projectId = process.env.FIREBASE_PROJECT_ID;
  const clientEmail = process.env.FIREBASE_CLIENT_EMAIL;
  const privateKey = process.env.FIREBASE_PRIVATE_KEY;
  const databaseURL = process.env.FIREBASE_DATABASE_URL;

  if (!projectId) {
    throw new Error('FIREBASE_PROJECT_ID environment variable is required');
  }

  if (!clientEmail) {
    throw new Error('FIREBASE_CLIENT_EMAIL environment variable is required');
  }

  if (!privateKey) {
    throw new Error('FIREBASE_PRIVATE_KEY environment variable is required');
  }

  // Handle private key formatting (replace \\n with actual newlines)
  const formattedPrivateKey = privateKey.replace(/\\n/g, '\n');

  return {
    projectId,
    clientEmail,
    privateKey: formattedPrivateKey,
    databaseURL
  };
}

/**
 * Initialize Firebase Admin SDK
 */
export async function initializeFirebaseAdmin(): Promise<App> {
  try {
    // Check if Firebase is already initialized
    const existingApps = getApps();
    if (existingApps.length > 0) {
      logger.info('[Firebase] Using existing Firebase Admin SDK instance');
      firebaseApp = existingApps[0];
      return firebaseApp;
    }

    // Load configuration
    const config = loadFirebaseConfig();
    
    logger.info('[Firebase] Initializing Firebase Admin SDK...', {
      projectId: config.projectId,
      clientEmail: config.clientEmail
    });

    // Initialize Firebase Admin SDK with service account credentials
    firebaseApp = initializeApp({
      credential: cert({
        projectId: config.projectId,
        clientEmail: config.clientEmail,
        privateKey: config.privateKey
      }),
      databaseURL: config.databaseURL,
      projectId: config.projectId
    });

    logger.info('[Firebase] Firebase Admin SDK initialized successfully');
    return firebaseApp;

  } catch (error) {
    logger.error('[Firebase] Failed to initialize Firebase Admin SDK:', error);
    throw new Error(`Firebase initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get Firestore database instance
 */
export async function getFirestoreDb(): Promise<Firestore> {
  try {
    if (!firestoreDb) {
      // Ensure Firebase is initialized
      if (!firebaseApp) {
        await initializeFirebaseAdmin();
      }

      if (!firebaseApp) {
        throw new Error('Firebase app not initialized');
      }

      // Initialize Firestore
      firestoreDb = getFirestore(firebaseApp);
      
      logger.info('[Firebase] Firestore database instance created');
    }

    return firestoreDb;
  } catch (error) {
    logger.error('[Firebase] Failed to get Firestore database:', error);
    throw new Error(`Firestore initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Validate Firebase connection
 */
export async function validateFirebaseConnection(): Promise<boolean> {
  try {
    logger.info('[Firebase] Validating Firebase connection...');

    const db = await getFirestoreDb();
    
    // Test connection by attempting to read from a test collection
    const testCollection = db.collection('_connection_test');
    const testDoc = testCollection.doc('test');
    
    // Try to read the document (this will test the connection)
    await testDoc.get();
    
    logger.info('[Firebase] Firebase connection validated successfully');
    return true;

  } catch (error) {
    logger.error('[Firebase] Firebase connection validation failed:', error);
    return false;
  }
}

/**
 * Get Firebase app instance (for advanced usage)
 */
export function getFirebaseApp(): App | null {
  return firebaseApp;
}

/**
 * Reset Firebase instances (useful for testing)
 */
export function resetFirebaseInstances(): void {
  firebaseApp = null;
  firestoreDb = null;
  logger.debug('[Firebase] Firebase instances reset');
}

/**
 * Get current Firebase configuration method
 */
export function getFirebaseConfigMethod(): 'json' | 'individual' | 'none' {
  if (process.env.FIREBASE_SERVICE_ACCOUNT_JSON) {
    return 'json';
  }

  if (process.env.FIREBASE_PROJECT_ID && process.env.FIREBASE_CLIENT_EMAIL && process.env.FIREBASE_PRIVATE_KEY) {
    return 'individual';
  }

  return 'none';
}

/**
 * Validate Firebase configuration without initializing
 */
export function validateFirebaseConfig(): {
  isValid: boolean;
  method: 'json' | 'individual' | 'none';
  errors: string[];
} {
  const method = getFirebaseConfigMethod();
  const errors: string[] = [];

  if (method === 'json') {
    try {
      const serviceAccountJson = process.env.FIREBASE_SERVICE_ACCOUNT_JSON!;
      const serviceAccount = JSON.parse(serviceAccountJson);

      if (!serviceAccount.project_id) errors.push('project_id missing from service account JSON');
      if (!serviceAccount.client_email) errors.push('client_email missing from service account JSON');
      if (!serviceAccount.private_key) errors.push('private_key missing from service account JSON');

    } catch (error) {
      errors.push(`Invalid JSON in FIREBASE_SERVICE_ACCOUNT_JSON: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  } else if (method === 'individual') {
    if (!process.env.FIREBASE_PROJECT_ID) errors.push('FIREBASE_PROJECT_ID environment variable is required');
    if (!process.env.FIREBASE_CLIENT_EMAIL) errors.push('FIREBASE_CLIENT_EMAIL environment variable is required');
    if (!process.env.FIREBASE_PRIVATE_KEY) errors.push('FIREBASE_PRIVATE_KEY environment variable is required');
  } else {
    errors.push('No Firebase configuration found. Provide either FIREBASE_SERVICE_ACCOUNT_JSON or individual environment variables.');
  }

  return {
    isValid: errors.length === 0,
    method,
    errors
  };
}

/**
 * Firebase health check
 */
export async function firebaseHealthCheck(): Promise<{
  status: 'healthy' | 'unhealthy';
  details: Record<string, unknown>;
}> {
  try {
    const configValidation = validateFirebaseConfig();
    const isConnected = await validateFirebaseConnection();

    // Get project ID from current config
    let projectId = 'not-configured';
    try {
      const config = loadFirebaseConfig();
      projectId = config.projectId;
    } catch {
      // Config loading failed, keep default
    }

    return {
      status: isConnected ? 'healthy' : 'unhealthy',
      details: {
        initialized: firebaseApp !== null,
        firestoreReady: firestoreDb !== null,
        connectionValid: isConnected,
        configMethod: configValidation.method,
        configValid: configValidation.isValid,
        configErrors: configValidation.errors,
        projectId
      }
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        initialized: false,
        firestoreReady: false,
        connectionValid: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

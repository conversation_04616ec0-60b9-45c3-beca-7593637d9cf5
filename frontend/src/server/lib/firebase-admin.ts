/**
 * Firebase Admin SDK Configuration
 * 
 * Server-side only Firebase Admin SDK initialization with proper error handling
 * and connection validation. Uses environment variables for configuration.
 */

import { initializeApp, getApps, cert, type App } from 'firebase-admin/app';
import { getFirestore, type Firestore } from 'firebase-admin/firestore';
import { logger } from '../../lib/logger';

// Firebase configuration interface
interface FirebaseConfig {
  projectId: string;
  clientEmail: string;
  privateKey: string;
  databaseURL?: string;
}

// Firebase Admin SDK instance
let firebaseApp: App | null = null;
let firestoreDb: Firestore | null = null;

/**
 * Load Firebase configuration from environment variables
 */
function loadFirebaseConfig(): FirebaseConfig {
  const projectId = process.env.FIREBASE_PROJECT_ID;
  const clientEmail = process.env.FIREBASE_CLIENT_EMAIL;
  const privateKey = process.env.FIREBASE_PRIVATE_KEY;
  const databaseURL = process.env.FIREBASE_DATABASE_URL;

  if (!projectId) {
    throw new Error('FIREBASE_PROJECT_ID environment variable is required');
  }

  if (!clientEmail) {
    throw new Error('FIREBASE_CLIENT_EMAIL environment variable is required');
  }

  if (!privateKey) {
    throw new Error('FIREBASE_PRIVATE_KEY environment variable is required');
  }

  // Handle private key formatting (replace \\n with actual newlines)
  const formattedPrivateKey = privateKey.replace(/\\n/g, '\n');

  return {
    projectId,
    clientEmail,
    privateKey: formattedPrivateKey,
    databaseURL
  };
}

/**
 * Initialize Firebase Admin SDK
 */
export async function initializeFirebaseAdmin(): Promise<App> {
  try {
    // Check if Firebase is already initialized
    const existingApps = getApps();
    if (existingApps.length > 0) {
      logger.info('[Firebase] Using existing Firebase Admin SDK instance');
      firebaseApp = existingApps[0];
      return firebaseApp;
    }

    // Load configuration
    const config = loadFirebaseConfig();
    
    logger.info('[Firebase] Initializing Firebase Admin SDK...', {
      projectId: config.projectId,
      clientEmail: config.clientEmail
    });

    // Initialize Firebase Admin SDK with service account credentials
    firebaseApp = initializeApp({
      credential: cert({
        projectId: config.projectId,
        clientEmail: config.clientEmail,
        privateKey: config.privateKey
      }),
      databaseURL: config.databaseURL,
      projectId: config.projectId
    });

    logger.info('[Firebase] Firebase Admin SDK initialized successfully');
    return firebaseApp;

  } catch (error) {
    logger.error('[Firebase] Failed to initialize Firebase Admin SDK:', error);
    throw new Error(`Firebase initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get Firestore database instance
 */
export async function getFirestoreDb(): Promise<Firestore> {
  try {
    if (!firestoreDb) {
      // Ensure Firebase is initialized
      if (!firebaseApp) {
        await initializeFirebaseAdmin();
      }

      if (!firebaseApp) {
        throw new Error('Firebase app not initialized');
      }

      // Initialize Firestore
      firestoreDb = getFirestore(firebaseApp);
      
      logger.info('[Firebase] Firestore database instance created');
    }

    return firestoreDb;
  } catch (error) {
    logger.error('[Firebase] Failed to get Firestore database:', error);
    throw new Error(`Firestore initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Validate Firebase connection
 */
export async function validateFirebaseConnection(): Promise<boolean> {
  try {
    logger.info('[Firebase] Validating Firebase connection...');

    const db = await getFirestoreDb();
    
    // Test connection by attempting to read from a test collection
    const testCollection = db.collection('_connection_test');
    const testDoc = testCollection.doc('test');
    
    // Try to read the document (this will test the connection)
    await testDoc.get();
    
    logger.info('[Firebase] Firebase connection validated successfully');
    return true;

  } catch (error) {
    logger.error('[Firebase] Firebase connection validation failed:', error);
    return false;
  }
}

/**
 * Get Firebase app instance (for advanced usage)
 */
export function getFirebaseApp(): App | null {
  return firebaseApp;
}

/**
 * Reset Firebase instances (useful for testing)
 */
export function resetFirebaseInstances(): void {
  firebaseApp = null;
  firestoreDb = null;
  logger.debug('[Firebase] Firebase instances reset');
}

/**
 * Firebase health check
 */
export async function firebaseHealthCheck(): Promise<{
  status: 'healthy' | 'unhealthy';
  details: Record<string, unknown>;
}> {
  try {
    const isConnected = await validateFirebaseConnection();
    
    return {
      status: isConnected ? 'healthy' : 'unhealthy',
      details: {
        initialized: firebaseApp !== null,
        firestoreReady: firestoreDb !== null,
        connectionValid: isConnected,
        projectId: process.env.FIREBASE_PROJECT_ID || 'not-configured'
      }
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        initialized: false,
        firestoreReady: false,
        connectionValid: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

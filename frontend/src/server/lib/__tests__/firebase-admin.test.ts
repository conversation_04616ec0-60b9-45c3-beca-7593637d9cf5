/**
 * Firebase Admin SDK Integration Tests
 * 
 * Tests to verify Firebase connection and basic read/write operations
 */

import { 
  initializeFirebaseAdmin, 
  getFirestoreDb, 
  validateFirebaseConnection,
  firebaseHealthCheck,
  resetFirebaseInstances 
} from '../firebase-admin';


// Mock the logger to avoid console output during tests
jest.mock('../../../lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

// Mock environment variables for testing
const mockEnvVars = {
  FIREBASE_PROJECT_ID: 'test-project-id',
  FIREBASE_CLIENT_EMAIL: '<EMAIL>',
  FIREBASE_PRIVATE_KEY: '-----BEGIN PRIVATE KEY-----\nMOCK_PRIVATE_KEY\n-----END PRIVATE KEY-----',
  FIREBASE_DATABASE_URL: 'https://test-project.firebaseio.com'
};

describe('Firebase Admin SDK Integration', () => {
  beforeEach(() => {
    // Reset Firebase instances before each test
    resetFirebaseInstances();
    
    // Set up mock environment variables
    Object.entries(mockEnvVars).forEach(([key, value]) => {
      process.env[key] = value;
    });
  });

  afterEach(() => {
    // Clean up environment variables
    Object.keys(mockEnvVars).forEach(key => {
      delete process.env[key];
    });
    
    // Reset Firebase instances after each test
    resetFirebaseInstances();
  });

  describe('Configuration Loading', () => {
    test('should throw error when FIREBASE_PROJECT_ID is missing', async () => {
      delete process.env.FIREBASE_PROJECT_ID;
      
      await expect(initializeFirebaseAdmin()).rejects.toThrow(
        'FIREBASE_PROJECT_ID environment variable is required'
      );
    });

    test('should throw error when FIREBASE_CLIENT_EMAIL is missing', async () => {
      delete process.env.FIREBASE_CLIENT_EMAIL;
      
      await expect(initializeFirebaseAdmin()).rejects.toThrow(
        'FIREBASE_CLIENT_EMAIL environment variable is required'
      );
    });

    test('should throw error when FIREBASE_PRIVATE_KEY is missing', async () => {
      delete process.env.FIREBASE_PRIVATE_KEY;
      
      await expect(initializeFirebaseAdmin()).rejects.toThrow(
        'FIREBASE_PRIVATE_KEY environment variable is required'
      );
    });
  });

  describe('Firebase Initialization', () => {
    test('should initialize Firebase Admin SDK with valid configuration', async () => {
      // This test will fail in CI/CD without real Firebase credentials
      // but demonstrates the expected behavior
      try {
        const app = await initializeFirebaseAdmin();
        expect(app).toBeDefined();
      } catch (error) {
        // Expected to fail without real credentials
        expect(error).toBeInstanceOf(Error);
      }
    });

    test('should return existing app instance on subsequent calls', async () => {
      try {
        const app1 = await initializeFirebaseAdmin();
        const app2 = await initializeFirebaseAdmin();
        expect(app1).toBe(app2);
      } catch (error) {
        // Expected to fail without real credentials
        expect(error).toBeInstanceOf(Error);
      }
    });
  });

  describe('Firestore Database', () => {
    test('should get Firestore database instance', async () => {
      try {
        const db = await getFirestoreDb();
        expect(db).toBeDefined();
      } catch (error) {
        // Expected to fail without real credentials
        expect(error).toBeInstanceOf(Error);
      }
    });
  });

  describe('Connection Validation', () => {
    test('should validate Firebase connection', async () => {
      const isValid = await validateFirebaseConnection();
      // Without real credentials, this should return false
      expect(typeof isValid).toBe('boolean');
    });

    test('should perform health check', async () => {
      const health = await firebaseHealthCheck();
      
      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('details');
      expect(['healthy', 'unhealthy']).toContain(health.status);
      expect(health.details).toHaveProperty('initialized');
      expect(health.details).toHaveProperty('firestoreReady');
      expect(health.details).toHaveProperty('connectionValid');
    });
  });

  describe('Basic Read/Write Operations (Integration)', () => {
    test('should perform basic Firestore operations when connected', async () => {
      try {
        const db = await getFirestoreDb();
        
        // Test document reference
        const testCollection = db.collection('_integration_test');
        const testDoc = testCollection.doc('test-doc');
        
        // Test write operation
        await testDoc.set({
          message: 'Firebase integration test',
          timestamp: new Date(),
          testData: {
            number: 42,
            boolean: true,
            array: [1, 2, 3]
          }
        });
        
        // Test read operation
        const docSnapshot = await testDoc.get();
        expect(docSnapshot.exists).toBe(true);
        
        const data = docSnapshot.data();
        expect(data).toHaveProperty('message', 'Firebase integration test');
        expect(data).toHaveProperty('testData');
        expect(data?.testData).toHaveProperty('number', 42);
        
        // Test delete operation
        await testDoc.delete();
        
        // Verify deletion
        const deletedDoc = await testDoc.get();
        expect(deletedDoc.exists).toBe(false);
        
      } catch (error) {
        // Expected to fail without real Firebase credentials
        // This test demonstrates the expected behavior with real credentials
        expect(error).toBeInstanceOf(Error);
      }
    });

    test('should handle collection queries', async () => {
      try {
        const db = await getFirestoreDb();
        
        // Test collection query
        const testCollection = db.collection('_integration_test');
        const querySnapshot = await testCollection.limit(1).get();
        
        expect(querySnapshot).toBeDefined();
        expect(typeof querySnapshot.size).toBe('number');
        
      } catch (error) {
        // Expected to fail without real Firebase credentials
        expect(error).toBeInstanceOf(Error);
      }
    });
  });
});

/**
 * Manual integration test function for real Firebase testing
 * This can be run manually with real Firebase credentials
 */
export async function manualFirebaseIntegrationTest(): Promise<void> {
  console.log('🔥 Starting manual Firebase integration test...');
  
  try {
    // Initialize Firebase
    console.log('1. Initializing Firebase Admin SDK...');
    await initializeFirebaseAdmin();
    console.log('✅ Firebase initialized');
    
    // Validate connection
    console.log('2. Validating connection...');
    const isConnected = await validateFirebaseConnection();
    console.log(`✅ Connection valid: ${isConnected}`);
    
    // Get Firestore instance
    console.log('3. Getting Firestore instance...');
    const db = await getFirestoreDb();
    console.log('✅ Firestore instance obtained');
    
    // Test basic operations
    console.log('4. Testing basic read/write operations...');
    const testDoc = db.collection('_manual_test').doc('integration-test');
    
    // Write test data
    await testDoc.set({
      message: 'Manual integration test successful',
      timestamp: new Date(),
      testId: Math.random().toString(36).substring(7)
    });
    console.log('✅ Write operation successful');
    
    // Read test data
    const docSnapshot = await testDoc.get();
    if (docSnapshot.exists) {
      console.log('✅ Read operation successful:', docSnapshot.data());
    } else {
      throw new Error('Document not found after write');
    }
    
    // Clean up
    await testDoc.delete();
    console.log('✅ Cleanup successful');
    
    // Health check
    console.log('5. Performing health check...');
    const health = await firebaseHealthCheck();
    console.log('✅ Health check result:', health);
    
    console.log('🎉 Manual Firebase integration test completed successfully!');
    
  } catch (error) {
    console.error('❌ Manual Firebase integration test failed:', error);
    throw error;
  }
}

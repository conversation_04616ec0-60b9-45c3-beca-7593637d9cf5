/**
 * Firebase Admin SDK Integration Tests
 * 
 * Tests to verify Firebase connection and basic read/write operations
 */

import {
  initializeFirebaseAdmin,
  getFirestoreDb,
  validateFirebaseConnection,
  firebaseHealthCheck,
  resetFirebaseInstances,
  getFirebaseConfigMethod,
  validateFirebaseConfig
} from '../firebase-admin';


// Mock the logger to avoid console output during tests
jest.mock('../../../lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

// Mock environment variables for testing
const mockEnvVars = {
  FIREBASE_PROJECT_ID: 'test-project-id',
  FIREBASE_CLIENT_EMAIL: '<EMAIL>',
  FIREBASE_PRIVATE_KEY: '-----BEGIN PRIVATE KEY-----\nMOCK_PRIVATE_KEY\n-----<PERSON>ND PRIVATE KEY-----',
  FIREBASE_DATABASE_URL: 'https://test-project.firebaseio.com'
};

// Mock service account JSON for testing
const mockServiceAccountJson = JSON.stringify({
  type: 'service_account',
  project_id: 'test-project-id',
  private_key_id: 'mock-key-id',
  private_key: '-----BEGIN PRIVATE KEY-----\nMOCK_PRIVATE_KEY\n-----END PRIVATE KEY-----',
  client_email: '<EMAIL>',
  client_id: '*********',
  auth_uri: 'https://accounts.google.com/o/oauth2/auth',
  token_uri: 'https://oauth2.googleapis.com/token',
  auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
  client_x509_cert_url: 'https://www.googleapis.com/robot/v1/metadata/x509/test%40test-project.iam.gserviceaccount.com',
  universe_domain: 'googleapis.com'
});

describe('Firebase Admin SDK Integration', () => {
  beforeEach(() => {
    // Reset Firebase instances before each test
    resetFirebaseInstances();
    
    // Set up mock environment variables
    Object.entries(mockEnvVars).forEach(([key, value]) => {
      process.env[key] = value;
    });
  });

  afterEach(() => {
    // Clean up environment variables
    Object.keys(mockEnvVars).forEach(key => {
      delete process.env[key];
    });
    
    // Reset Firebase instances after each test
    resetFirebaseInstances();
  });

  describe('Configuration Loading', () => {
    describe('Individual Environment Variables', () => {
      test('should throw error when FIREBASE_PROJECT_ID is missing', async () => {
        delete process.env.FIREBASE_PROJECT_ID;

        await expect(initializeFirebaseAdmin()).rejects.toThrow(
          'FIREBASE_PROJECT_ID environment variable is required'
        );
      });

      test('should throw error when FIREBASE_CLIENT_EMAIL is missing', async () => {
        delete process.env.FIREBASE_CLIENT_EMAIL;

        await expect(initializeFirebaseAdmin()).rejects.toThrow(
          'FIREBASE_CLIENT_EMAIL environment variable is required'
        );
      });

      test('should throw error when FIREBASE_PRIVATE_KEY is missing', async () => {
        delete process.env.FIREBASE_PRIVATE_KEY;

        await expect(initializeFirebaseAdmin()).rejects.toThrow(
          'FIREBASE_PRIVATE_KEY environment variable is required'
        );
      });
    });

    describe('Service Account JSON', () => {
      beforeEach(() => {
        // Clear individual env vars to test JSON approach
        delete process.env.FIREBASE_PROJECT_ID;
        delete process.env.FIREBASE_CLIENT_EMAIL;
        delete process.env.FIREBASE_PRIVATE_KEY;
      });

      test('should load configuration from FIREBASE_SERVICE_ACCOUNT_JSON', async () => {
        process.env.FIREBASE_SERVICE_ACCOUNT_JSON = mockServiceAccountJson;

        try {
          const app = await initializeFirebaseAdmin();
          expect(app).toBeDefined();
        } catch (error) {
          // Expected to fail without real credentials, but should not fail on config parsing
          expect(error).toBeInstanceOf(Error);
          expect((error as Error).message).not.toContain('environment variable is required');
        }
      });

      test('should throw error when JSON is invalid', async () => {
        process.env.FIREBASE_SERVICE_ACCOUNT_JSON = 'invalid-json';

        await expect(initializeFirebaseAdmin()).rejects.toThrow(
          'Invalid FIREBASE_SERVICE_ACCOUNT_JSON'
        );
      });

      test('should throw error when required fields are missing from JSON', async () => {
        const incompleteJson = JSON.stringify({
          type: 'service_account',
          // missing project_id, client_email, private_key
        });
        process.env.FIREBASE_SERVICE_ACCOUNT_JSON = incompleteJson;

        await expect(initializeFirebaseAdmin()).rejects.toThrow(
          'project_id is missing from service account JSON'
        );
      });

      test('should prioritize JSON over individual environment variables', async () => {
        // Set both JSON and individual vars
        process.env.FIREBASE_SERVICE_ACCOUNT_JSON = mockServiceAccountJson;
        process.env.FIREBASE_PROJECT_ID = 'should-be-ignored';
        process.env.FIREBASE_CLIENT_EMAIL = '<EMAIL>';
        process.env.FIREBASE_PRIVATE_KEY = 'should-be-ignored';

        try {
          const app = await initializeFirebaseAdmin();
          expect(app).toBeDefined();
          // The JSON config should be used (project_id: 'test-project-id')
        } catch (error) {
          // Expected to fail without real credentials
          expect(error).toBeInstanceOf(Error);
        }
      });
    });
  });

  describe('Firebase Initialization', () => {
    test('should initialize Firebase Admin SDK with valid configuration', async () => {
      // This test will fail in CI/CD without real Firebase credentials
      // but demonstrates the expected behavior
      try {
        const app = await initializeFirebaseAdmin();
        expect(app).toBeDefined();
      } catch (error) {
        // Expected to fail without real credentials
        expect(error).toBeInstanceOf(Error);
      }
    });

    test('should return existing app instance on subsequent calls', async () => {
      try {
        const app1 = await initializeFirebaseAdmin();
        const app2 = await initializeFirebaseAdmin();
        expect(app1).toBe(app2);
      } catch (error) {
        // Expected to fail without real credentials
        expect(error).toBeInstanceOf(Error);
      }
    });
  });

  describe('Firestore Database', () => {
    test('should get Firestore database instance', async () => {
      try {
        const db = await getFirestoreDb();
        expect(db).toBeDefined();
      } catch (error) {
        // Expected to fail without real credentials
        expect(error).toBeInstanceOf(Error);
      }
    });
  });

  describe('Configuration Validation', () => {
    test('should detect JSON configuration method', () => {
      process.env.FIREBASE_SERVICE_ACCOUNT_JSON = mockServiceAccountJson;
      delete process.env.FIREBASE_PROJECT_ID;

      const method = getFirebaseConfigMethod();
      expect(method).toBe('json');
    });

    test('should detect individual variables configuration method', () => {
      delete process.env.FIREBASE_SERVICE_ACCOUNT_JSON;
      // mockEnvVars are set in beforeEach

      const method = getFirebaseConfigMethod();
      expect(method).toBe('individual');
    });

    test('should detect no configuration', () => {
      delete process.env.FIREBASE_SERVICE_ACCOUNT_JSON;
      delete process.env.FIREBASE_PROJECT_ID;
      delete process.env.FIREBASE_CLIENT_EMAIL;
      delete process.env.FIREBASE_PRIVATE_KEY;

      const method = getFirebaseConfigMethod();
      expect(method).toBe('none');
    });

    test('should validate JSON configuration', () => {
      process.env.FIREBASE_SERVICE_ACCOUNT_JSON = mockServiceAccountJson;
      delete process.env.FIREBASE_PROJECT_ID;

      const validation = validateFirebaseConfig();
      expect(validation.isValid).toBe(true);
      expect(validation.method).toBe('json');
      expect(validation.errors).toHaveLength(0);
    });

    test('should validate individual variables configuration', () => {
      delete process.env.FIREBASE_SERVICE_ACCOUNT_JSON;
      // mockEnvVars are set in beforeEach

      const validation = validateFirebaseConfig();
      expect(validation.isValid).toBe(true);
      expect(validation.method).toBe('individual');
      expect(validation.errors).toHaveLength(0);
    });

    test('should detect invalid JSON configuration', () => {
      process.env.FIREBASE_SERVICE_ACCOUNT_JSON = 'invalid-json';
      delete process.env.FIREBASE_PROJECT_ID;

      const validation = validateFirebaseConfig();
      expect(validation.isValid).toBe(false);
      expect(validation.method).toBe('json');
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors[0]).toContain('Invalid JSON');
    });
  });

  describe('Connection Validation', () => {
    test('should validate Firebase connection', async () => {
      const isValid = await validateFirebaseConnection();
      // Without real credentials, this should return false
      expect(typeof isValid).toBe('boolean');
    });

    test('should perform health check with configuration details', async () => {
      const health = await firebaseHealthCheck();

      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('details');
      expect(['healthy', 'unhealthy']).toContain(health.status);
      expect(health.details).toHaveProperty('initialized');
      expect(health.details).toHaveProperty('firestoreReady');
      expect(health.details).toHaveProperty('connectionValid');
      expect(health.details).toHaveProperty('configMethod');
      expect(health.details).toHaveProperty('configValid');
      expect(health.details).toHaveProperty('projectId');
    });
  });

  describe('Basic Read/Write Operations (Integration)', () => {
    test('should perform basic Firestore operations when connected', async () => {
      try {
        const db = await getFirestoreDb();
        
        // Test document reference
        const testCollection = db.collection('_integration_test');
        const testDoc = testCollection.doc('test-doc');
        
        // Test write operation
        await testDoc.set({
          message: 'Firebase integration test',
          timestamp: new Date(),
          testData: {
            number: 42,
            boolean: true,
            array: [1, 2, 3]
          }
        });
        
        // Test read operation
        const docSnapshot = await testDoc.get();
        expect(docSnapshot.exists).toBe(true);
        
        const data = docSnapshot.data();
        expect(data).toHaveProperty('message', 'Firebase integration test');
        expect(data).toHaveProperty('testData');
        expect(data?.testData).toHaveProperty('number', 42);
        
        // Test delete operation
        await testDoc.delete();
        
        // Verify deletion
        const deletedDoc = await testDoc.get();
        expect(deletedDoc.exists).toBe(false);
        
      } catch (error) {
        // Expected to fail without real Firebase credentials
        // This test demonstrates the expected behavior with real credentials
        expect(error).toBeInstanceOf(Error);
      }
    });

    test('should handle collection queries', async () => {
      try {
        const db = await getFirestoreDb();
        
        // Test collection query
        const testCollection = db.collection('_integration_test');
        const querySnapshot = await testCollection.limit(1).get();
        
        expect(querySnapshot).toBeDefined();
        expect(typeof querySnapshot.size).toBe('number');
        
      } catch (error) {
        // Expected to fail without real Firebase credentials
        expect(error).toBeInstanceOf(Error);
      }
    });
  });
});

/**
 * Manual integration test function for real Firebase testing
 * This can be run manually with real Firebase credentials
 */
export async function manualFirebaseIntegrationTest(): Promise<void> {
  console.log('🔥 Starting manual Firebase integration test...');
  
  try {
    // Initialize Firebase
    console.log('1. Initializing Firebase Admin SDK...');
    await initializeFirebaseAdmin();
    console.log('✅ Firebase initialized');
    
    // Validate connection
    console.log('2. Validating connection...');
    const isConnected = await validateFirebaseConnection();
    console.log(`✅ Connection valid: ${isConnected}`);
    
    // Get Firestore instance
    console.log('3. Getting Firestore instance...');
    const db = await getFirestoreDb();
    console.log('✅ Firestore instance obtained');
    
    // Test basic operations
    console.log('4. Testing basic read/write operations...');
    const testDoc = db.collection('_manual_test').doc('integration-test');
    
    // Write test data
    await testDoc.set({
      message: 'Manual integration test successful',
      timestamp: new Date(),
      testId: Math.random().toString(36).substring(7)
    });
    console.log('✅ Write operation successful');
    
    // Read test data
    const docSnapshot = await testDoc.get();
    if (docSnapshot.exists) {
      console.log('✅ Read operation successful:', docSnapshot.data());
    } else {
      throw new Error('Document not found after write');
    }
    
    // Clean up
    await testDoc.delete();
    console.log('✅ Cleanup successful');
    
    // Health check
    console.log('5. Performing health check...');
    const health = await firebaseHealthCheck();
    console.log('✅ Health check result:', health);
    
    console.log('🎉 Manual Firebase integration test completed successfully!');
    
  } catch (error) {
    console.error('❌ Manual Firebase integration test failed:', error);
    throw error;
  }
}

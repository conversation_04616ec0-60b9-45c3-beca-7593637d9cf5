/**
 * Discount tRPC Router
 * 
 * This file defines the tRPC procedures for discount management,
 * providing type-safe API endpoints for CRUD operations and discount calculations.
 */

import { z } from 'zod';
import { router, publicProcedure, adminProcedure, handleTRPCError } from '../../shared/trpc/trpc';
import { DefaultDiscountService } from './discount.service';
import { InMemoryDiscountStorageProvider } from './storage/in-memory.provider';
import { DefaultDiscountRepository } from './discount.repository';
import { DefaultDiscountEngine, createDiscountEngineFactory } from './discount.engine';
import {
  CreateDiscountSchema,
  UpdateDiscountSchema,
  GetDiscountByIdSchema,
  DeleteDiscountSchema,
  ListDiscountsSchema,
  CalculateDiscountSchema,
  GetUsageStatsSchema,
  DiscountResponseSchema,
  DiscountCalculationResultSchema,
  PaginatedDiscountsResponseSchema,
  UsageStatsResponseSchema,
} from './discount.validation';


// Initialize discount service (singleton pattern)
const storageProvider = new InMemoryDiscountStorageProvider();
const repository = new DefaultDiscountRepository({ storageProvider });
const engineFactory = createDiscountEngineFactory();
const engine = engineFactory.createEngineWithDefaults();
const discountService = new DefaultDiscountService(repository, engine);

// Initialize the service and add sample data for testing
discountService.initialize().then(async () => {
  // Add a sample discount for testing if none exist
  try {
    const activeDiscounts = await discountService.getActiveDiscounts();
    if (activeDiscounts.success && activeDiscounts.data && activeDiscounts.data.length === 0) {
      // Create a sample discount for testing
      await discountService.createDiscount({
        name: 'Test Discount - 10% off',
        description: 'Sample discount for testing - 10% off with ₹50 cap on orders above ₹200',
        type: 'PERCENTAGE_CAP',
        isActive: true,
        validFrom: new Date(),
        validTo: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        percentage: 10,
        maxDiscountAmount: 50,
        minCartValue: 200,
        maxUsage: 1000,
      });

      // eslint-disable-next-line no-console
      console.log('✅ Sample discount created for testing');
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Failed to create sample discount:', error);
  }
}).catch((error) => {
  // eslint-disable-next-line no-console
  console.error('Failed to initialize discount service:', error);
});

/**
 * Discount router with all CRUD and calculation procedures
 */
export const discountRouter = router({
  /**
   * Create a new discount (Admin only)
   */
  create: adminProcedure
    .input(CreateDiscountSchema)
    .output(DiscountResponseSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const result = await discountService.createDiscount({
          name: input.name,
          description: input.description,
          type: input.type,
          isActive: input.isActive,
          validFrom: input.validFrom,
          validTo: input.validTo,
          percentage: input.percentage,
          maxDiscountAmount: input.maxDiscountAmount,
          minCartValue: input.minCartValue,
          maxUsage: input.maxUsage,
        });

        if (!result.success || !result.data) {
          throw new Error(result.error || 'Failed to create discount');
        }

        return result.data;
      } catch (error) {
        handleTRPCError(error, 'discount.create');
        throw error; // This won't be reached due to handleTRPCError throwing
      }
    }),

  /**
   * List discounts with pagination and filtering (Admin only)
   */
  list: adminProcedure
    .input(ListDiscountsSchema)
    .output(PaginatedDiscountsResponseSchema)
    .query(async ({ input }) => {
      try {
        const result = await discountService.listDiscounts(
          {
            isActive: input.filters?.isActive,
            type: input.filters?.type,
            validAt: input.filters?.validAt,
            search: input.filters?.search,
          },
          {
            page: input.page,
            limit: input.limit,
          }
        );

        if (!result.success || !result.data) {
          throw new Error(result.error || 'Failed to list discounts');
        }

        return result.data;
      } catch (error) {
        handleTRPCError(error, 'discount.list');
        throw error;
      }
    }),

  /**
   * Get discount by ID (Admin only)
   */
  getById: adminProcedure
    .input(GetDiscountByIdSchema)
    .output(DiscountResponseSchema.nullable())
    .query(async ({ input }) => {
      try {
        const result = await discountService.getDiscountById(input.id);

        if (!result.success) {
          throw new Error(result.error || 'Failed to get discount');
        }

        return result.data || null;
      } catch (error) {
        handleTRPCError(error, 'discount.getById');
        throw error;
      }
    }),

  /**
   * Update discount (Admin only)
   */
  update: adminProcedure
    .input(UpdateDiscountSchema)
    .output(DiscountResponseSchema)
    .mutation(async ({ input }) => {
      try {
        const { id, ...updateData } = input;
        const result = await discountService.updateDiscount(id, updateData);

        if (!result.success || !result.data) {
          throw new Error(result.error || 'Failed to update discount');
        }

        return result.data;
      } catch (error) {
        handleTRPCError(error, 'discount.update');
        throw error;
      }
    }),

  /**
   * Delete discount (Admin only)
   */
  delete: adminProcedure
    .input(DeleteDiscountSchema)
    .output(z.object({ success: z.boolean() }))
    .mutation(async ({ input }) => {
      try {
        const result = await discountService.deleteDiscount(input.id);

        if (!result.success) {
          throw new Error(result.error || 'Failed to delete discount');
        }

        return { success: true };
      } catch (error) {
        handleTRPCError(error, 'discount.delete');
        throw error;
      }
    }),

  /**
   * Calculate discounts for cart items (Public - used by cart system)
   */
  calculate: publicProcedure
    .input(CalculateDiscountSchema)
    .output(DiscountCalculationResultSchema)
    .query(async ({ input }) => {
      try {
        const result = await discountService.calculateDiscounts({
          cartItems: input.cartItems,
          applyDiscounts: true,
        });

        if (!result.success || !result.data) {
          // For cart calculations, we should gracefully degrade
          // Return no discount result instead of throwing error
          const originalTotal = input.cartItems.reduce(
            (sum, item) => sum + (item.pricePerUnit * item.quantity),
            0
          );

          return {
            totalDiscount: 0,
            appliedDiscounts: [],
            originalTotal,
            finalTotal: originalTotal,
            savings: 0,
          };
        }

        return result.data;
      } catch (error) {
        // Log error but don't throw - graceful degradation for cart calculations
        // eslint-disable-next-line no-console
        console.error('Discount calculation failed, using fallback:', error);

        const originalTotal = input.cartItems.reduce(
          (sum, item) => sum + (item.pricePerUnit * item.quantity),
          0
        );

        return {
          totalDiscount: 0,
          appliedDiscounts: [],
          originalTotal,
          finalTotal: originalTotal,
          savings: 0,
        };
      }
    }),

  /**
   * Get usage statistics (Admin only)
   */
  getUsageStats: adminProcedure
    .input(GetUsageStatsSchema)
    .output(UsageStatsResponseSchema)
    .query(async ({ input }) => {
      try {
        const result = await discountService.getUsageAnalytics({
          discountId: input.discountId,
          dateFrom: input.dateFrom,
          dateTo: input.dateTo,
          includeDetails: true,
        });

        if (!result.success || !result.data) {
          throw new Error(result.error || 'Failed to get usage statistics');
        }

        // Transform the data to match the expected schema
        return {
          discountId: result.data.discountId,
          totalApplications: result.data.totalApplications,
          totalSavings: result.data.totalSavings,
          averageDiscountAmount: result.data.averageDiscountAmount,
          dateRange: {
            from: input.dateFrom || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Default to 30 days ago
            to: input.dateTo || new Date(),
          },
          dailyStats: result.data.usageByDate.map((stat: { date: string; applications: number; savings: number }) => ({
            date: new Date(stat.date),
            applications: stat.applications,
            savings: stat.savings,
          })),
        };
      } catch (error) {
        handleTRPCError(error, 'discount.getUsageStats');
        throw error;
      }
    }),

  /**
   * Get active discounts (Public - for frontend to check available discounts)
   */
  getActive: publicProcedure
    .output(z.array(DiscountResponseSchema))
    .query(async () => {
      try {
        const result = await discountService.getActiveDiscounts();

        if (!result.success || !result.data) {
          return [];
        }

        return result.data;
      } catch (error) {
        // Log error but return empty array for graceful degradation
        // eslint-disable-next-line no-console
        console.error('Failed to get active discounts:', error);
        return [];
      }
    }),

  /**
   * Check discount eligibility for a cart (Public)
   */
  checkEligibility: publicProcedure
    .input(z.object({
      discountId: z.string().uuid(),
      cartItems: z.array(z.object({
        skuId: z.number().int().positive(),
        variantSkuId: z.number().int().positive().optional(),
        quantity: z.number().int().min(1),
        pricePerUnit: z.number().min(0),
        mrpPerUnit: z.number().min(0),
      })),
    }))
    .output(z.object({
      eligible: z.boolean(),
      reason: z.string().optional(),
      estimatedDiscount: z.number().optional(),
    }))
    .query(async ({ input }) => {
      try {
        const result = await discountService.checkDiscountEligibility(
          input.discountId,
          input.cartItems
        );

        if (!result.success || !result.data) {
          return {
            eligible: false,
            reason: result.error || 'Unable to check eligibility',
          };
        }

        return result.data;
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to check discount eligibility:', error);
        return {
          eligible: false,
          reason: 'Unable to check eligibility',
        };
      }
    }),
});

export type DiscountRouter = typeof discountRouter;
/**
 * Shared utility functions for the discount system
 * 
 * This file contains reusable utility functions to eliminate code duplication
 * and provide consistent patterns across the discount system components.
 */

import { logger } from '../../../lib/logger';
import { DataUtils, ValidationUtils, MathUtils, DateUtils } from '../../shared/utils/common';

/**
 * Service result creation helpers
 */
export const ServiceResultHelpers = {
  /**
   * Create a successful service result
   */
  success: <T>(data: T): { success: true; data: T } => ({
    success: true,
    data
  }),

  /**
   * Create a failed service result
   */
  failure: (error: string, code: string): { success: false; error: string; code: string } => ({
    success: false,
    error,
    code
  }),

  /**
   * Create a service result from an error
   */
  fromError: (error: unknown, defaultCode = 'OPERATION_ERROR'): { success: false; error: string; code: string } => {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorCode = (error as { code?: string })?.code || defaultCode;
    
    return {
      success: false,
      error: errorMessage,
      code: errorCode
    };
  }
};

/**
 * Logging pattern helpers
 */
export const LoggingHelpers = {
  /**
   * Log operation start with consistent format
   */
  logOperationStart: (component: string, operation: string, details?: Record<string, unknown>) => {
    logger.debug(`[${component}] Starting ${operation}`, details);
  },

  /**
   * Log operation success with consistent format
   */
  logOperationSuccess: (component: string, operation: string, details?: Record<string, unknown>) => {
    logger.debug(`[${component}] ${operation} completed successfully`, details);
  },

  /**
   * Log operation failure with consistent format
   */
  logOperationFailure: (component: string, operation: string, error: unknown, details?: Record<string, unknown>) => {
    logger.error(`[${component}] ${operation} failed:`, { error, ...details });
  },

  /**
   * Log operation info with consistent format
   */
  logOperationInfo: (component: string, message: string, details?: Record<string, unknown>) => {
    logger.info(`[${component}] ${message}`, details);
  },

  /**
   * Log operation warning with consistent format
   */
  logOperationWarning: (component: string, message: string, details?: Record<string, unknown>) => {
    logger.warn(`[${component}] ${message}`, details);
  }
};

/**
 * Error handling patterns
 */
export const ErrorHandlingHelpers = {
  /**
   * Execute operation with consistent error handling and logging
   */
  executeWithErrorHandling: async <T>(
    component: string,
    operation: string,
    fn: () => Promise<T>,
    details?: Record<string, unknown>
  ): Promise<T> => {
    LoggingHelpers.logOperationStart(component, operation, details);
    
    try {
      const result = await fn();
      LoggingHelpers.logOperationSuccess(component, operation);
      return result;
    } catch (error) {
      LoggingHelpers.logOperationFailure(component, operation, error, details);
      throw error;
    }
  },

  /**
   * Execute operation with service result pattern
   */
  executeWithServiceResult: async <T>(
    component: string,
    operation: string,
    fn: () => Promise<T>,
    details?: Record<string, unknown>
  ): Promise<{ success: true; data: T } | { success: false; error: string; code: string }> => {
    try {
      const result = await ErrorHandlingHelpers.executeWithErrorHandling(component, operation, fn, details);
      return ServiceResultHelpers.success(result);
    } catch (error) {
      return ServiceResultHelpers.fromError(error);
    }
  }
};

/**
 * Validation pattern helpers
 */
export const ValidationHelpers = {
  /**
   * Validate discount data with consistent error handling
   */
  validateDiscountData: async (
    component: string,
    discount: unknown,
    validationFn: (discount: unknown) => Promise<string[]> | string[]
  ): Promise<string[]> => {
    try {
      LoggingHelpers.logOperationStart(component, 'validateDiscountData');
      
      const errors = await validationFn(discount);
      
      if (errors.length > 0) {
        LoggingHelpers.logOperationWarning(component, `Validation failed with ${errors.length} errors`, { errors });
      } else {
        LoggingHelpers.logOperationSuccess(component, 'validateDiscountData');
      }
      
      return errors;
    } catch (error) {
      LoggingHelpers.logOperationFailure(component, 'validateDiscountData', error);
      return ['Validation failed due to unexpected error'];
    }
  },

  /**
   * Execute validation with error recording
   */
  executeValidation: (
    component: string,
    validationName: string,
    validationFn: () => boolean,
    errorMessage: string
  ): string[] => {
    const errors: string[] = [];
    
    try {
      if (!validationFn()) {
        errors.push(errorMessage);
      }
    } catch (error) {
      LoggingHelpers.logOperationFailure(component, validationName, error);
      errors.push(`${validationName} failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    
    return errors;
  }
};

/**
 * Health check pattern helpers
 */
export const HealthCheckHelpers = {
  /**
   * Determine overall health status from multiple component statuses
   */
  determineOverallHealth: (
    statuses: Array<'healthy' | 'degraded' | 'unhealthy'>
  ): 'healthy' | 'degraded' | 'unhealthy' => {
    if (statuses.includes('unhealthy')) {
      return 'unhealthy';
    }
    if (statuses.includes('degraded')) {
      return 'degraded';
    }
    return 'healthy';
  },

  /**
   * Calculate health status based on ratio and thresholds
   */
  calculateHealthFromRatio: (
    ratio: number,
    degradedThreshold: number,
    unhealthyThreshold: number
  ): 'healthy' | 'degraded' | 'unhealthy' => {
    if (ratio >= unhealthyThreshold) {
      return 'unhealthy';
    }
    if (ratio >= degradedThreshold) {
      return 'degraded';
    }
    return 'healthy';
  },

  /**
   * Calculate health status based on success rate
   */
  calculateHealthFromSuccessRate: (
    successRate: number,
    minHealthyRate: number,
    minDegradedRate: number
  ): 'healthy' | 'degraded' | 'unhealthy' => {
    if (successRate >= minHealthyRate) {
      return 'healthy';
    }
    if (successRate >= minDegradedRate) {
      return 'degraded';
    }
    return 'unhealthy';
  }
};

/**
 * Metrics collection helpers
 */
export const MetricsHelpers = {
  /**
   * Record operation timing
   */
  recordOperationTime: (
    operationTimes: number[],
    startTime: number,
    maxRecords = 100
  ): void => {
    const operationTime = Date.now() - startTime;
    operationTimes.push(operationTime);
    
    // Keep only last N operation times to prevent memory leaks
    if (operationTimes.length > maxRecords) {
      operationTimes.splice(0, operationTimes.length - maxRecords);
    }
  },

  /**
   * Calculate average from array of numbers
   */
  calculateAverage: (numbers: number[]): number => {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  },

  /**
   * Update operation counters
   */
  updateOperationCounters: (
    metrics: {
      totalOperations: number;
      successfulOperations: number;
      failedOperations: number;
      lastOperationAt?: Date;
    },
    success: boolean
  ): void => {
    metrics.totalOperations++;
    if (success) {
      metrics.successfulOperations++;
    } else {
      metrics.failedOperations++;
    }
    metrics.lastOperationAt = new Date();
  },

  /**
   * Calculate success rate
   */
  calculateSuccessRate: (successful: number, total: number): number => {
    return total > 0 ? successful / total : 1;
  }
};

/**
 * Cache management helpers
 */
export const CacheHelpers = {
  /**
   * Check if cache entry is valid
   */
  isCacheValid: (timestamp: number, ttlMs: number): boolean => {
    const now = Date.now();
    const age = now - timestamp;
    return age < ttlMs;
  },

  /**
   * Create cache entry
   */
  createCacheEntry: <T>(data: T): { data: T; timestamp: number } => ({
    data,
    timestamp: Date.now()
  }),

  /**
   * Get cache age in milliseconds
   */
  getCacheAge: (timestamp: number): number => {
    return Date.now() - timestamp;
  }
};

/**
 * Data transformation helpers (using shared utilities)
 */
export const DataTransformHelpers = {
  // Import shared utilities
  deepClone: DataUtils.deepClone,
  sanitizeString: DataUtils.sanitizeString,
  roundToDecimals: DataUtils.roundToDecimals,

  // Keep discount-specific transformations
  /**
   * Convert date to timestamp (discount-specific utility)
   */
  dateToTimestamp: (date: Date = new Date()): Date => {
    return new Date(date.getTime());
  }
};

/**
 * Configuration helpers
 */
export const ConfigHelpers = {
  /**
   * Merge configurations with defaults
   */
  mergeWithDefaults: <T>(defaults: T, overrides: Partial<T>): T => {
    return { ...defaults, ...overrides };
  },

  /**
   * Validate configuration object
   */
  validateConfig: <T>(
    config: T,
    validators: Array<{ key: keyof T; validator: (value: any) => boolean; message: string }>
  ): string[] => {
    const errors: string[] = [];
    
    for (const { key, validator, message } of validators) {
      try {
        if (!validator(config[key])) {
          errors.push(message);
        }
      } catch (error) {
        errors.push(`Configuration validation failed for ${String(key)}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
    
    return errors;
  }
};

// Utility helpers are already exported individually above
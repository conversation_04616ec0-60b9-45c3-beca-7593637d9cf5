/**
 * Firestore Storage Provider for Discount System
 * 
 * Implements the DiscountStorageProvider interface using Firebase Firestore
 * as the persistent storage backend. Maintains the same API as the in-memory provider.
 */

import type { Firestore, DocumentSnapshot, QuerySnapshot, Transaction } from 'firebase-admin/firestore';
import { FieldValue, Timestamp } from 'firebase-admin/firestore';
import type {
  Discount,
  DiscountUsageEntry,
  DiscountType,
  DiscountFilters
} from '../discount.types';
import type {
  DiscountStorageProvider,
  EnhancedDiscountFilters,
  UsageStats,
  StorageProviderConfig,
  StorageTransaction
} from './storage.interface';
import type { PaginationParams, PaginatedResponse } from '../../../shared/types/api.types';
import { getFirestoreDb } from '../../../lib/firebase-admin';
import { StorageUtils } from './storage.utils';
import { 
  StorageValidationError, 
  StorageConstraintError,
  StorageTransactionError,
  StorageError
} from './storage.errors';
import { DiscountNotFoundError } from '../discount.errors';
import { getStorageConfig } from '../discount.config';
import { logger } from '../../../../lib/logger';

// Firestore collection names
const COLLECTIONS = {
  DISCOUNTS: 'discounts',
  USAGE_ENTRIES: 'discount_usage_entries'
} as const;

// Firestore document structure for discounts
interface FirestoreDiscount extends Omit<Discount, 'createdAt' | 'updatedAt' | 'validFrom' | 'validTo'> {
  createdAt: FirebaseFirestore.Timestamp;
  updatedAt: FirebaseFirestore.Timestamp;
  validFrom: FirebaseFirestore.Timestamp;
  validTo: FirebaseFirestore.Timestamp;
}

// Firestore document structure for usage entries
interface FirestoreUsageEntry extends Omit<DiscountUsageEntry, 'appliedAt'> {
  appliedAt: FirebaseFirestore.Timestamp;
}

// Transaction context for Firestore operations
class FirestoreTransaction implements StorageTransaction {
  private operations: Array<() => Promise<void>> = [];
  private committed = false;
  private rolledBack = false;

  constructor(
    private db: Firestore,
    private transaction: Transaction,
    private transactionId: string
  ) {}

  async create(discount: Omit<Discount, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>): Promise<Discount> {
    if (this.committed || this.rolledBack) {
      throw new StorageTransactionError('create', 'Transaction already completed');
    }

    const id = StorageUtils.generateId();
    const now = StorageUtils.getCurrentTimestamp();
    const newDiscount: Discount = {
      ...StorageUtils.sanitizeDiscountInput(discount),
      id,
      createdAt: now,
      updatedAt: now,
      usageCount: 0
    };

    // Validate the discount
    const validationErrors = StorageUtils.validateDiscountData(newDiscount);
    if (validationErrors.length > 0) {
      throw new StorageValidationError('discount', newDiscount, validationErrors.join(', '));
    }

    // Convert to Firestore format
    const firestoreDiscount = this.convertToFirestoreDiscount(newDiscount);
    
    // Add to transaction
    const docRef = this.db.collection(COLLECTIONS.DISCOUNTS).doc(id);
    this.transaction.set(docRef, firestoreDiscount);

    return StorageUtils.cloneDiscount(newDiscount);
  }

  async update(id: string, updates: Partial<Discount>): Promise<Discount> {
    if (this.committed || this.rolledBack) {
      throw new StorageTransactionError('update', 'Transaction already completed');
    }

    const docRef = this.db.collection(COLLECTIONS.DISCOUNTS).doc(id);
    const docSnapshot = await this.transaction.get(docRef);

    if (!docSnapshot.exists) {
      throw new DiscountNotFoundError(id);
    }

    const existingDiscount = this.convertFromFirestoreDiscount(docSnapshot.data() as FirestoreDiscount, id);
    const updatedDiscount: Discount = {
      ...existingDiscount,
      ...updates,
      updatedAt: StorageUtils.getCurrentTimestamp()
    };

    // Validate the updated discount
    const validationErrors = StorageUtils.validateDiscountData(updatedDiscount);
    if (validationErrors.length > 0) {
      throw new StorageValidationError('discount', updatedDiscount, validationErrors.join(', '));
    }

    // Convert to Firestore format and update
    const firestoreDiscount = this.convertToFirestoreDiscount(updatedDiscount);
    this.transaction.set(docRef, firestoreDiscount);

    return StorageUtils.cloneDiscount(updatedDiscount);
  }

  async delete(id: string): Promise<boolean> {
    if (this.committed || this.rolledBack) {
      throw new StorageTransactionError('delete', 'Transaction already completed');
    }

    const docRef = this.db.collection(COLLECTIONS.DISCOUNTS).doc(id);
    const docSnapshot = await this.transaction.get(docRef);

    if (!docSnapshot.exists) {
      return false;
    }

    this.transaction.delete(docRef);
    return true;
  }

  async commit(): Promise<void> {
    if (this.committed || this.rolledBack) {
      throw new StorageTransactionError('commit', 'Transaction already completed');
    }

    try {
      // Firestore transactions are automatically committed when the transaction function completes
      // This is just a marker for our interface compliance
      this.committed = true;
      logger.debug(`[FirestoreStorageProvider] Transaction ${this.transactionId} committed successfully`);
    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Transaction ${this.transactionId} commit failed:`, error);
      throw new StorageTransactionError('commit', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async rollback(): Promise<void> {
    if (this.committed || this.rolledBack) {
      throw new StorageTransactionError('rollback', 'Transaction already completed');
    }

    try {
      // Firestore transactions auto-rollback on failure, but we can mark as rolled back
      this.rolledBack = true;
      logger.debug(`[FirestoreStorageProvider] Transaction ${this.transactionId} rolled back`);
    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Transaction ${this.transactionId} rollback failed:`, error);
      throw new StorageTransactionError('rollback', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  private convertToFirestoreDiscount(discount: Discount): FirestoreDiscount {
    return {
      ...discount,
      createdAt: Timestamp.fromDate(discount.createdAt),
      updatedAt: Timestamp.fromDate(discount.updatedAt),
      validFrom: Timestamp.fromDate(discount.validFrom),
      validTo: Timestamp.fromDate(discount.validTo)
    };
  }

  private convertFromFirestoreDiscount(firestoreDiscount: FirestoreDiscount, id: string): Discount {
    return {
      ...firestoreDiscount,
      id,
      createdAt: firestoreDiscount.createdAt.toDate(),
      updatedAt: firestoreDiscount.updatedAt.toDate(),
      validFrom: firestoreDiscount.validFrom.toDate(),
      validTo: firestoreDiscount.validTo.toDate()
    };
  }
}

/**
 * Firestore implementation of the DiscountStorageProvider interface
 */
export class FirestoreDiscountStorageProvider implements DiscountStorageProvider {
  private db: Firestore | null = null;
  private config: StorageProviderConfig;
  private initialized = false;
  private transactionCounter = 0;

  constructor(customConfig?: Partial<StorageProviderConfig>) {
    const storageConfig = getStorageConfig();
    this.config = {
      name: 'firestore',
      version: '1.0.0',
      options: {
        maxDiscounts: storageConfig.maxDiscounts,
        maxUsageEntries: storageConfig.maxUsageEntries,
        projectId: process.env.FIREBASE_PROJECT_ID
      },
      ...customConfig
    };
  }

  getConfig(): StorageProviderConfig {
    return { ...this.config };
  }

  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      logger.info('[FirestoreStorageProvider] Initializing Firestore storage provider...');
      
      // Get Firestore database instance
      this.db = await getFirestoreDb();
      
      // Verify connection by attempting a simple read
      await this.db.collection(COLLECTIONS.DISCOUNTS).limit(1).get();
      
      this.initialized = true;
      logger.info('[FirestoreStorageProvider] Firestore storage provider initialized successfully');
      
    } catch (error) {
      logger.error('[FirestoreStorageProvider] Failed to initialize Firestore storage provider:', error);
      throw new StorageError('initialization', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async cleanup(): Promise<void> {
    try {
      // Firestore doesn't require explicit cleanup, but we can reset our state
      this.initialized = false;
      this.db = null;
      this.transactionCounter = 0;
      
      logger.info('[FirestoreStorageProvider] Firestore storage provider cleaned up successfully');
    } catch (error) {
      logger.error('[FirestoreStorageProvider] Failed to cleanup Firestore storage provider:', error);
      throw new StorageError('cleanup', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  private ensureInitialized(): void {
    if (!this.initialized || !this.db) {
      throw new StorageError('operation', 'Storage provider not initialized');
    }
  }

  private convertToFirestoreDiscount(discount: Discount): FirestoreDiscount {
    if (!this.db) {
      throw new StorageError('operation', 'Database not initialized');
    }

    return {
      ...discount,
      createdAt: Timestamp.fromDate(discount.createdAt),
      updatedAt: Timestamp.fromDate(discount.updatedAt),
      validFrom: Timestamp.fromDate(discount.validFrom),
      validTo: Timestamp.fromDate(discount.validTo)
    };
  }

  private convertFromFirestoreDiscount(firestoreDiscount: FirestoreDiscount, id: string): Discount {
    return {
      ...firestoreDiscount,
      id,
      createdAt: firestoreDiscount.createdAt.toDate(),
      updatedAt: firestoreDiscount.updatedAt.toDate(),
      validFrom: firestoreDiscount.validFrom.toDate(),
      validTo: firestoreDiscount.validTo.toDate()
    };
  }

  private convertToFirestoreUsageEntry(entry: DiscountUsageEntry): FirestoreUsageEntry {
    if (!this.db) {
      throw new StorageError('operation', 'Database not initialized');
    }

    return {
      ...entry,
      appliedAt: Timestamp.fromDate(entry.appliedAt)
    };
  }

  private convertFromFirestoreUsageEntry(firestoreEntry: FirestoreUsageEntry): DiscountUsageEntry {
    return {
      ...firestoreEntry,
      appliedAt: firestoreEntry.appliedAt.toDate()
    };
  }

  async create(discount: Omit<Discount, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>): Promise<Discount> {
    this.ensureInitialized();

    const id = StorageUtils.generateId();
    const now = StorageUtils.getCurrentTimestamp();
    const newDiscount: Discount = {
      ...StorageUtils.sanitizeDiscountInput(discount),
      id,
      createdAt: now,
      updatedAt: now,
      usageCount: 0
    };

    // Validate the discount
    const validationErrors = StorageUtils.validateDiscountData(newDiscount);
    if (validationErrors.length > 0) {
      throw new StorageValidationError('discount', newDiscount, validationErrors.join(', '));
    }

    try {
      // Convert to Firestore format
      const firestoreDiscount = this.convertToFirestoreDiscount(newDiscount);

      // Save to Firestore
      await this.db!.collection(COLLECTIONS.DISCOUNTS).doc(id).set(firestoreDiscount);

      logger.debug(`[FirestoreStorageProvider] Created discount: ${id}`);
      return StorageUtils.cloneDiscount(newDiscount);

    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Failed to create discount:`, error);
      throw new StorageError('create', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async findById(id: string): Promise<Discount | null> {
    this.ensureInitialized();

    try {
      const docSnapshot = await this.db!.collection(COLLECTIONS.DISCOUNTS).doc(id).get();

      if (!docSnapshot.exists) {
        return null;
      }

      const firestoreDiscount = docSnapshot.data() as FirestoreDiscount;
      const discount = this.convertFromFirestoreDiscount(firestoreDiscount, id);

      return StorageUtils.cloneDiscount(discount);

    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Failed to find discount by ID ${id}:`, error);
      throw new StorageError('findById', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async findAll(filters?: EnhancedDiscountFilters, pagination?: PaginationParams): Promise<PaginatedResponse<Discount>> {
    this.ensureInitialized();

    try {
      let query: any = this.db!.collection(COLLECTIONS.DISCOUNTS);

      // Apply filters
      if (filters) {
        if (filters.isActive !== undefined) {
          query = query.where('isActive', '==', filters.isActive);
        }
        if (filters.type) {
          query = query.where('type', '==', filters.type);
        }
        if (filters.validAt) {
          const validAtTimestamp = Timestamp.fromDate(filters.validAt);
          query = query.where('validFrom', '<=', validAtTimestamp)
                      .where('validTo', '>=', validAtTimestamp);
        }
      }

      // Apply sorting
      if (filters?.sortBy) {
        const direction = filters.sortOrder === 'desc' ? 'desc' : 'asc';
        query = query.orderBy(filters.sortBy, direction);
      } else {
        // Default sort by creation date
        query = query.orderBy('createdAt', 'desc');
      }

      // Apply pagination
      const limit = pagination?.limit || 50;
      const page = pagination?.page || 1;
      const offset = (page - 1) * limit;

      query = query.limit(limit).offset(offset);

      const querySnapshot = await query.get();
      const discounts: Discount[] = [];

      querySnapshot.forEach((doc: any) => {
        const firestoreDiscount = doc.data() as FirestoreDiscount;
        const discount = this.convertFromFirestoreDiscount(firestoreDiscount, doc.id);
        discounts.push(discount);
      });

      // Get total count for pagination (this is expensive, consider caching)
      const totalQuery = this.db!.collection(COLLECTIONS.DISCOUNTS);
      const totalSnapshot = await totalQuery.count().get();
      const total = totalSnapshot.data().count;
      const totalPages = Math.ceil(total / limit);

      return {
        items: discounts.map(d => StorageUtils.cloneDiscount(d)),
        total,
        page,
        limit,
        totalPages
      };

    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Failed to find all discounts:`, error);
      throw new StorageError('findAll', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async update(id: string, updates: Partial<Discount>): Promise<Discount> {
    this.ensureInitialized();

    try {
      const docRef = this.db!.collection(COLLECTIONS.DISCOUNTS).doc(id);
      const docSnapshot = await docRef.get();

      if (!docSnapshot.exists) {
        throw new DiscountNotFoundError(id);
      }

      const existingDiscount = this.convertFromFirestoreDiscount(docSnapshot.data() as FirestoreDiscount, id);
      const updatedDiscount: Discount = {
        ...existingDiscount,
        ...updates,
        updatedAt: StorageUtils.getCurrentTimestamp()
      };

      // Validate the updated discount
      const validationErrors = StorageUtils.validateDiscountData(updatedDiscount);
      if (validationErrors.length > 0) {
        throw new StorageValidationError('discount', updatedDiscount, validationErrors.join(', '));
      }

      // Convert to Firestore format and update
      const firestoreDiscount = this.convertToFirestoreDiscount(updatedDiscount);
      await docRef.set(firestoreDiscount);

      logger.debug(`[FirestoreStorageProvider] Updated discount: ${id}`);
      return StorageUtils.cloneDiscount(updatedDiscount);

    } catch (error) {
      if (error instanceof DiscountNotFoundError || error instanceof StorageValidationError) {
        throw error;
      }
      logger.error(`[FirestoreStorageProvider] Failed to update discount ${id}:`, error);
      throw new StorageError('update', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async delete(id: string): Promise<boolean> {
    this.ensureInitialized();

    try {
      const docRef = this.db!.collection(COLLECTIONS.DISCOUNTS).doc(id);
      const docSnapshot = await docRef.get();

      if (!docSnapshot.exists) {
        return false;
      }

      await docRef.delete();

      logger.debug(`[FirestoreStorageProvider] Deleted discount: ${id}`);
      return true;

    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Failed to delete discount ${id}:`, error);
      throw new StorageError('delete', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async findActiveDiscounts(validAt?: Date): Promise<Discount[]> {
    this.ensureInitialized();

    try {
      const checkDate = validAt || new Date();
      const checkTimestamp = Timestamp.fromDate(checkDate);

      const query = this.db!.collection(COLLECTIONS.DISCOUNTS)
        .where('isActive', '==', true)
        .where('validFrom', '<=', checkTimestamp)
        .where('validTo', '>=', checkTimestamp)
        .orderBy('validFrom')
        .orderBy('validTo');

      const querySnapshot = await query.get();
      const discounts: Discount[] = [];

      querySnapshot.forEach(doc => {
        const firestoreDiscount = doc.data() as FirestoreDiscount;
        const discount = this.convertFromFirestoreDiscount(firestoreDiscount, doc.id);
        discounts.push(discount);
      });

      return discounts.map(d => StorageUtils.cloneDiscount(d));

    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Failed to find active discounts:`, error);
      throw new StorageError('findActiveDiscounts', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async findByType(type: DiscountType): Promise<Discount[]> {
    this.ensureInitialized();

    try {
      const query = this.db!.collection(COLLECTIONS.DISCOUNTS)
        .where('type', '==', type)
        .orderBy('createdAt', 'desc');

      const querySnapshot = await query.get();
      const discounts: Discount[] = [];

      querySnapshot.forEach(doc => {
        const firestoreDiscount = doc.data() as FirestoreDiscount;
        const discount = this.convertFromFirestoreDiscount(firestoreDiscount, doc.id);
        discounts.push(discount);
      });

      return discounts.map(d => StorageUtils.cloneDiscount(d));

    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Failed to find discounts by type ${type}:`, error);
      throw new StorageError('findByType', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async bulkDelete(ids: string[]): Promise<number> {
    this.ensureInitialized();

    if (ids.length === 0) {
      return 0;
    }

    try {
      // Firestore batch operations are limited to 500 operations
      const batchSize = 500;
      let deletedCount = 0;

      for (let i = 0; i < ids.length; i += batchSize) {
        const batch = this.db!.batch();
        const batchIds = ids.slice(i, i + batchSize);

        for (const id of batchIds) {
          const docRef = this.db!.collection(COLLECTIONS.DISCOUNTS).doc(id);
          batch.delete(docRef);
        }

        await batch.commit();
        deletedCount += batchIds.length;
      }

      logger.debug(`[FirestoreStorageProvider] Bulk deleted ${deletedCount} discounts`);
      return deletedCount;

    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Failed to bulk delete discounts:`, error);
      throw new StorageError('bulkDelete', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async incrementUsage(id: string): Promise<void> {
    this.ensureInitialized();

    try {
      const docRef = this.db!.collection(COLLECTIONS.DISCOUNTS).doc(id);

      await this.db!.runTransaction(async (transaction) => {
        const doc = await transaction.get(docRef);

        if (!doc.exists) {
          throw new DiscountNotFoundError(id);
        }

        const currentUsageCount = doc.data()?.usageCount || 0;
        transaction.update(docRef, {
          usageCount: currentUsageCount + 1,
          updatedAt: Timestamp.now()
        });
      });

      logger.debug(`[FirestoreStorageProvider] Incremented usage for discount: ${id}`);

    } catch (error) {
      if (error instanceof DiscountNotFoundError) {
        throw error;
      }
      logger.error(`[FirestoreStorageProvider] Failed to increment usage for discount ${id}:`, error);
      throw new StorageError('incrementUsage', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async recordUsage(entry: Omit<DiscountUsageEntry, 'appliedAt'>): Promise<void> {
    this.ensureInitialized();

    try {
      const usageEntry: DiscountUsageEntry = {
        ...entry,
        appliedAt: StorageUtils.getCurrentTimestamp()
      };

      // Convert to Firestore format
      const firestoreEntry = this.convertToFirestoreUsageEntry(usageEntry);

      // Generate a unique ID for the usage entry
      const entryId = StorageUtils.generateId();

      // Save to Firestore
      await this.db!.collection(COLLECTIONS.USAGE_ENTRIES).doc(entryId).set(firestoreEntry);

      // Also increment the usage count on the discount
      await this.incrementUsage(entry.discountId);

      logger.debug(`[FirestoreStorageProvider] Recorded usage entry: ${entryId}`);

    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Failed to record usage:`, error);
      throw new StorageError('recordUsage', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async getUsageStats(filters?: {
    discountId?: string;
    dateFrom?: Date;
    dateTo?: Date;
  }): Promise<UsageStats> {
    this.ensureInitialized();

    try {
      let query: any = this.db!.collection(COLLECTIONS.USAGE_ENTRIES);

      // Apply filters
      if (filters?.discountId) {
        query = query.where('discountId', '==', filters.discountId);
      }

      if (filters?.dateFrom) {
        const fromTimestamp = Timestamp.fromDate(filters.dateFrom);
        query = query.where('appliedAt', '>=', fromTimestamp);
      }

      if (filters?.dateTo) {
        const toTimestamp = Timestamp.fromDate(filters.dateTo);
        query = query.where('appliedAt', '<=', toTimestamp);
      }

      const querySnapshot = await query.get();
      const usageEntries: DiscountUsageEntry[] = [];

      querySnapshot.forEach((doc: any) => {
        const firestoreEntry = doc.data() as FirestoreUsageEntry;
        const entry = this.convertFromFirestoreUsageEntry(firestoreEntry);
        usageEntries.push(entry);
      });

      // Calculate statistics
      const totalApplications = usageEntries.length;
      const totalSavings = usageEntries.reduce((sum, entry) => sum + entry.discountAmount, 0);
      const averageDiscountAmount = totalApplications > 0 ? totalSavings / totalApplications : 0;

      // Determine date range
      const dateFrom = filters?.dateFrom || (usageEntries.length > 0 ?
        new Date(Math.min(...usageEntries.map(e => e.appliedAt.getTime()))) : new Date());
      const dateTo = filters?.dateTo || (usageEntries.length > 0 ?
        new Date(Math.max(...usageEntries.map(e => e.appliedAt.getTime()))) : new Date());

      return {
        discountId: filters?.discountId,
        totalApplications,
        totalSavings,
        averageDiscountAmount,
        dateRange: {
          from: dateFrom,
          to: dateTo
        }
      };

    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Failed to get usage stats:`, error);
      throw new StorageError('getUsageStats', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async search(query: string, filters?: DiscountFilters): Promise<Discount[]> {
    this.ensureInitialized();

    try {
      // Firestore doesn't have full-text search, so we'll do a simple name/description search
      // For production, consider using Algolia or Elasticsearch for better search
      let firestoreQuery = this.db!.collection(COLLECTIONS.DISCOUNTS);

      // Apply basic filters first
      if (filters?.isActive !== undefined) {
        firestoreQuery = firestoreQuery.where('isActive', '==', filters.isActive);
      }
      if (filters?.type) {
        firestoreQuery = firestoreQuery.where('type', '==', filters.type);
      }

      const querySnapshot = await firestoreQuery.get();
      const discounts: Discount[] = [];

      querySnapshot.forEach(doc => {
        const firestoreDiscount = doc.data() as FirestoreDiscount;
        const discount = this.convertFromFirestoreDiscount(firestoreDiscount, doc.id);

        // Simple text search in name and description
        const searchTerm = query.toLowerCase();
        const nameMatch = discount.name.toLowerCase().includes(searchTerm);
        const descriptionMatch = discount.description?.toLowerCase().includes(searchTerm);

        if (nameMatch || descriptionMatch) {
          discounts.push(discount);
        }
      });

      return discounts.map(d => StorageUtils.cloneDiscount(d));

    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Failed to search discounts:`, error);
      throw new StorageError('search', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async count(filters?: DiscountFilters): Promise<number> {
    this.ensureInitialized();

    try {
      let query = this.db!.collection(COLLECTIONS.DISCOUNTS);

      // Apply filters
      if (filters?.isActive !== undefined) {
        query = query.where('isActive', '==', filters.isActive);
      }
      if (filters?.type) {
        query = query.where('type', '==', filters.type);
      }
      if (filters?.validAt) {
        const validAtTimestamp = Timestamp.fromDate(filters.validAt);
        query = query.where('validFrom', '<=', validAtTimestamp)
                    .where('validTo', '>=', validAtTimestamp);
      }

      const countSnapshot = await query.count().get();
      return countSnapshot.data().count;

    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Failed to count discounts:`, error);
      throw new StorageError('count', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async exists(id: string): Promise<boolean> {
    this.ensureInitialized();

    try {
      const docSnapshot = await this.db!.collection(COLLECTIONS.DISCOUNTS).doc(id).get();
      return docSnapshot.exists;

    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Failed to check if discount exists:`, error);
      throw new StorageError('exists', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async validateDiscount(discount: Partial<Discount>): Promise<string[]> {
    // Use the same validation logic as the in-memory provider
    return StorageUtils.validateDiscountData(discount);
  }

  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details?: Record<string, unknown>;
  }> {
    try {
      if (!this.initialized || !this.db) {
        return {
          status: 'unhealthy',
          details: {
            initialized: this.initialized,
            dbConnected: this.db !== null,
            error: 'Storage provider not initialized'
          }
        };
      }

      // Test basic connectivity
      await this.db.collection(COLLECTIONS.DISCOUNTS).limit(1).get();

      // Get basic stats
      const discountCount = await this.count();
      const usageStats = await this.getUsageStats();

      return {
        status: 'healthy',
        details: {
          initialized: this.initialized,
          dbConnected: true,
          discountCount,
          totalUsageEntries: usageStats.totalApplications,
          provider: this.config.name,
          version: this.config.version
        }
      };

    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Health check failed:`, error);
      return {
        status: 'unhealthy',
        details: {
          initialized: this.initialized,
          dbConnected: this.db !== null,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  async beginTransaction(): Promise<StorageTransaction> {
    this.ensureInitialized();

    try {
      const transactionId = `tx_${++this.transactionCounter}_${Date.now()}`;

      // For now, return a simple transaction wrapper
      // In a full implementation, you'd need to handle Firestore's transaction model differently
      return new Promise((resolve) => {
        this.db!.runTransaction(async (t) => {
          const firestoreTransaction = new FirestoreTransaction(this.db!, t, transactionId);
          resolve(firestoreTransaction);
          return Promise.resolve();
        });
      });

    } catch (error) {
      logger.error(`[FirestoreStorageProvider] Failed to begin transaction:`, error);
      throw new StorageError('beginTransaction', error instanceof Error ? error.message : 'Unknown error');
    }
  }
}

# Firestore Setup Guide for Discount System

This guide explains how to set up Firebase Firestore for the discount system storage provider.

## Prerequisites

1. **Firebase Project**: You need a Firebase project with Firestore enabled
2. **Firebase CLI**: Install the Firebase CLI tool
3. **Service Account**: Create a service account with Firestore permissions

## Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----"
FIREBASE_DATABASE_URL=https://your-project.firebaseio.com
```

### Getting Firebase Credentials

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Go to **Project Settings** > **Service Accounts**
4. Click **Generate New Private Key**
5. Download the JSON file and extract the required values:
   - `project_id` → `FIREBASE_PROJECT_ID`
   - `client_email` → `FIREBASE_CLIENT_EMAIL`
   - `private_key` → `FIREBASE_PRIVATE_KEY` (keep the \n characters)

## Firestore Database Setup

### 1. Enable Firestore

1. In Firebase Console, go to **Firestore Database**
2. Click **Create Database**
3. Choose **Start in production mode** (recommended)
4. Select a location for your database

### 2. Security Rules

Set up the following Firestore security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Discount documents - admin access only
    match /discounts/{discountId} {
      allow read, write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Usage entries - admin access only
    match /discount_usage_entries/{entryId} {
      allow read, write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Connection test collection - allow server access
    match /_connection_test/{document} {
      allow read, write: if true;
    }
  }
}
```

### 3. Deploy Firestore Indexes

The discount system requires specific indexes for optimal performance. Deploy them using the Firebase CLI:

```bash
# Navigate to the project root
cd /path/to/your/project

# Deploy the indexes
firebase deploy --only firestore:indexes --project your-project-id
```

The indexes are defined in `firestore.indexes.json` and include:

- **Active discounts lookup**: `isActive + createdAt`
- **Type-based filtering**: `type + createdAt`
- **Date range queries**: `validFrom + validTo + isActive`
- **Usage tracking**: `discountId + appliedAt`
- **Search optimization**: Individual field indexes for `name`, `description`, `usageCount`

## Collection Structure

### Discounts Collection (`discounts`)

```typescript
interface FirestoreDiscount {
  name: string;
  description?: string;
  type: 'PERCENTAGE_CAP';
  isActive: boolean;
  validFrom: FirebaseFirestore.Timestamp;
  validTo: FirebaseFirestore.Timestamp;
  percentage: number;
  maxDiscountAmount: number;
  minCartValue?: number;
  maxUsage?: number;
  usageCount: number;
  createdAt: FirebaseFirestore.Timestamp;
  updatedAt: FirebaseFirestore.Timestamp;
}
```

### Usage Entries Collection (`discount_usage_entries`)

```typescript
interface FirestoreUsageEntry {
  discountId: string;
  cartId: string;
  discountAmount: number;
  cartTotal: number;
  appliedAt: FirebaseFirestore.Timestamp;
}
```

## Testing the Setup

### 1. Connection Test

Run the manual integration test to verify your setup:

```typescript
import { manualFirebaseIntegrationTest } from '@/server/lib/__tests__/firebase-admin.test';

// Run this in a test environment
await manualFirebaseIntegrationTest();
```

### 2. Storage Provider Test

Test the Firestore storage provider:

```typescript
import { FirestoreDiscountStorageProvider } from '@/server/features/discount/storage/firestore.provider';

const provider = new FirestoreDiscountStorageProvider();
await provider.initialize();

// Test health check
const health = await provider.healthCheck();
console.log('Health status:', health);
```

## Switching to Firestore Provider

To use the Firestore storage provider instead of in-memory storage, update the discount router:

```typescript
// In discount.router.ts
import { FirestoreDiscountStorageProvider } from './storage/firestore.provider';

// Replace this line:
// const storageProvider = new InMemoryDiscountStorageProvider();

// With this:
const storageProvider = new FirestoreDiscountStorageProvider();
```

## Performance Considerations

1. **Index Usage**: All queries are designed to use the defined indexes
2. **Pagination**: Uses offset-based pagination (consider cursor-based for large datasets)
3. **Count Queries**: Total count queries can be expensive; consider caching
4. **Search**: Basic text search is implemented; consider Algolia for advanced search
5. **Transactions**: Firestore transactions have limitations (500 operations max)

## Monitoring and Maintenance

1. **Monitor Usage**: Check Firestore usage in Firebase Console
2. **Index Performance**: Monitor query performance and add indexes as needed
3. **Security**: Regularly review security rules
4. **Backup**: Set up automated backups for production data

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Verify service account credentials
2. **Permission Denied**: Check Firestore security rules
3. **Index Errors**: Deploy missing indexes using Firebase CLI
4. **Connection Timeout**: Check network connectivity and Firebase project status

### Debug Mode

Enable debug logging by setting the environment variable:

```bash
DEBUG=firestore
```

This will provide detailed logs for Firestore operations.

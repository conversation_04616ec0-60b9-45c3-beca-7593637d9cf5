# Firestore Setup Guide for Discount System

This guide explains how to set up Firebase Firestore for the discount system storage provider.

## Prerequisites

1. **Firebase Project**: You need a Firebase project with Firestore enabled
2. **Firebase CLI**: Install the Firebase CLI tool
3. **Service Account**: Create a service account with Firestore permissions

## Environment Variables

Our Firebase Admin SDK supports two configuration approaches:

### Option 1: Single JSON String (Recommended)

Use a single environment variable containing the entire service account JSON:

```bash
# Firebase Configuration - Single JSON approach
FIREBASE_SERVICE_ACCOUNT_JSON='{"type":"service_account","project_id":"your-project-id","private_key_id":"your-key-id","private_key":"-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----","client_email":"*******","client_id":"*********","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com","universe_domain":"googleapis.com"}'

# Optional: Override database URL if needed
FIREBASE_DATABASE_URL=https://your-project.firebaseio.com
```

### Option 2: Individual Variables (Legacy)

Use separate environment variables for each field:

```bash
# Firebase Configuration - Individual variables
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=*******
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----"
FIREBASE_DATABASE_URL=https://your-project.firebaseio.com
```

**Note**: If both approaches are configured, the JSON approach takes precedence.

### Getting Firebase Credentials

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Go to **Project Settings** > **Service Accounts**
4. Click **Generate New Private Key**
5. Download the JSON file

### Converting Service Account JSON to Environment Variable

#### Method 1: Using Our Conversion Script (Recommended)

We provide a utility script to make this process easy:

```bash
# Convert from file
node scripts/convert-firebase-json.js /path/to/your/service-account.json

# Convert from stdin
cat /path/to/your/service-account.json | node scripts/convert-firebase-json.js --stdin
```

The script will:
- Validate the JSON structure
- Convert it to the proper format
- Provide ready-to-use environment variable declarations
- Verify the conversion worked correctly

#### Method 2: Using Command Line Tools

```bash
# Convert JSON file to single-line string
cat /path/to/your/service-account.json | jq -c .
```

#### Method 3: Using Node.js

```javascript
const fs = require('fs');
const serviceAccount = JSON.parse(fs.readFileSync('/path/to/your/service-account.json', 'utf8'));
const jsonString = JSON.stringify(serviceAccount);
console.log(jsonString);
```

#### Method 4: Manual Conversion

1. Open your service account JSON file
2. Remove all whitespace and newlines to make it a single line
3. Copy the result to your environment variable (no escaping needed)

### For Individual Variables (Legacy Approach)

Extract the required values from the JSON file:
- `project_id` → `FIREBASE_PROJECT_ID`
- `client_email` → `FIREBASE_CLIENT_EMAIL`
- `private_key` → `FIREBASE_PRIVATE_KEY` (keep the \n characters)

## Firestore Database Setup

### 1. Enable Firestore

1. In Firebase Console, go to **Firestore Database**
2. Click **Create Database**
3. Choose **Start in production mode** (recommended)
4. Select a location for your database

### 2. Security Rules

Set up the following Firestore security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Discount documents - admin access only
    match /discounts/{discountId} {
      allow read, write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Usage entries - admin access only
    match /discount_usage_entries/{entryId} {
      allow read, write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Connection test collection - allow server access
    match /_connection_test/{document} {
      allow read, write: if true;
    }
  }
}
```

### 3. Deploy Firestore Indexes

The discount system requires specific indexes for optimal performance. Deploy them using the Firebase CLI:

```bash
# Navigate to the project root
cd /path/to/your/project

# Deploy the indexes
firebase deploy --only firestore:indexes --project your-project-id
```

The indexes are defined in `firestore.indexes.json` and include:

- **Active discounts lookup**: `isActive + createdAt`
- **Type-based filtering**: `type + createdAt`
- **Date range queries**: `validFrom + validTo + isActive`
- **Usage tracking**: `discountId + appliedAt`
- **Search optimization**: Individual field indexes for `name`, `description`, `usageCount`

## Collection Structure

### Discounts Collection (`discounts`)

```typescript
interface FirestoreDiscount {
  name: string;
  description?: string;
  type: 'PERCENTAGE_CAP';
  isActive: boolean;
  validFrom: FirebaseFirestore.Timestamp;
  validTo: FirebaseFirestore.Timestamp;
  percentage: number;
  maxDiscountAmount: number;
  minCartValue?: number;
  maxUsage?: number;
  usageCount: number;
  createdAt: FirebaseFirestore.Timestamp;
  updatedAt: FirebaseFirestore.Timestamp;
}
```

### Usage Entries Collection (`discount_usage_entries`)

```typescript
interface FirestoreUsageEntry {
  discountId: string;
  cartId: string;
  discountAmount: number;
  cartTotal: number;
  appliedAt: FirebaseFirestore.Timestamp;
}
```

## Testing the Setup

### 1. Connection Test

Run the manual integration test to verify your setup:

```typescript
import { manualFirebaseIntegrationTest } from '@/server/lib/__tests__/firebase-admin.test';

// Run this in a test environment
await manualFirebaseIntegrationTest();
```

### 2. Storage Provider Test

Test the Firestore storage provider:

```typescript
import { FirestoreDiscountStorageProvider } from '@/server/features/discount/storage/firestore.provider';

const provider = new FirestoreDiscountStorageProvider();
await provider.initialize();

// Test health check
const health = await provider.healthCheck();
console.log('Health status:', health);
```

## Switching to Firestore Provider

To use the Firestore storage provider instead of in-memory storage, update the discount router:

```typescript
// In discount.router.ts
import { FirestoreDiscountStorageProvider } from './storage/firestore.provider';

// Replace this line:
// const storageProvider = new InMemoryDiscountStorageProvider();

// With this:
const storageProvider = new FirestoreDiscountStorageProvider();
```

## Performance Considerations

1. **Index Usage**: All queries are designed to use the defined indexes
2. **Pagination**: Uses offset-based pagination (consider cursor-based for large datasets)
3. **Count Queries**: Total count queries can be expensive; consider caching
4. **Search**: Basic text search is implemented; consider Algolia for advanced search
5. **Transactions**: Firestore transactions have limitations (500 operations max)

## Monitoring and Maintenance

1. **Monitor Usage**: Check Firestore usage in Firebase Console
2. **Index Performance**: Monitor query performance and add indexes as needed
3. **Security**: Regularly review security rules
4. **Backup**: Set up automated backups for production data

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Verify service account credentials
2. **Permission Denied**: Check Firestore security rules
3. **Index Errors**: Deploy missing indexes using Firebase CLI
4. **Connection Timeout**: Check network connectivity and Firebase project status

### Debug Mode

Enable debug logging by setting the environment variable:

```bash
DEBUG=firestore
```

This will provide detailed logs for Firestore operations.

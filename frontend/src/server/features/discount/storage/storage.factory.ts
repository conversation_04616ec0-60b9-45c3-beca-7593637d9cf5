/**
 * Storage Provider Factory
 * 
 * Factory for creating different storage providers based on configuration.
 * Supports both in-memory and Firestore storage providers.
 */

import type { 
  DiscountStorageProvider, 
  StorageProviderConfig,
  StorageProviderFactory 
} from './storage.interface';
import { InMemoryDiscountStorageProvider } from './in-memory.provider';
import { FirestoreDiscountStorageProvider } from './firestore.provider';
import { logger } from '../../../../lib/logger';

// Supported storage provider types
export type StorageProviderType = 'in-memory' | 'firestore';

// Configuration for storage provider creation
export interface StorageFactoryConfig {
  type: StorageProviderType;
  options?: Record<string, unknown>;
}

/**
 * Default storage provider factory implementation
 */
export class DefaultStorageProviderFactory implements StorageProviderFactory {
  
  async createProvider(config: StorageProviderConfig): Promise<DiscountStorageProvider> {
    const providerType = config.name as StorageProviderType;
    
    logger.info(`[StorageFactory] Creating storage provider: ${providerType}`);
    
    switch (providerType) {
      case 'in-memory':
        return new InMemoryDiscountStorageProvider(config);
        
      case 'firestore':
        return new FirestoreDiscountStorageProvider(config);
        
      default:
        throw new Error(`Unsupported storage provider type: ${providerType}`);
    }
  }
  
  getSupportedProviders(): string[] {
    return ['in-memory', 'firestore'];
  }
}

/**
 * Create a storage provider based on environment configuration
 */
export async function createStorageProvider(
  type?: StorageProviderType,
  options?: Record<string, unknown>
): Promise<DiscountStorageProvider> {
  // Default to environment variable or fallback to in-memory
  const providerType = type || 
    (process.env.DISCOUNT_STORAGE_PROVIDER as StorageProviderType) || 
    'in-memory';
  
  const config: StorageProviderConfig = {
    name: providerType,
    version: '1.0.0',
    options: options || {}
  };
  
  const factory = new DefaultStorageProviderFactory();
  const provider = await factory.createProvider(config);
  
  // Initialize the provider
  if (provider.initialize) {
    await provider.initialize();
  }
  
  logger.info(`[StorageFactory] Storage provider '${providerType}' created and initialized`);
  return provider;
}

/**
 * Create storage provider with automatic fallback
 * 
 * Attempts to create the preferred provider, falls back to in-memory if it fails
 */
export async function createStorageProviderWithFallback(
  preferredType: StorageProviderType = 'firestore',
  fallbackType: StorageProviderType = 'in-memory'
): Promise<DiscountStorageProvider> {
  try {
    logger.info(`[StorageFactory] Attempting to create preferred storage provider: ${preferredType}`);
    return await createStorageProvider(preferredType);
    
  } catch (error) {
    logger.warn(`[StorageFactory] Failed to create ${preferredType} provider, falling back to ${fallbackType}:`, error);
    
    try {
      return await createStorageProvider(fallbackType);
    } catch (fallbackError) {
      logger.error(`[StorageFactory] Failed to create fallback provider ${fallbackType}:`, fallbackError);
      throw new Error(`Failed to create both preferred (${preferredType}) and fallback (${fallbackType}) storage providers`);
    }
  }
}

/**
 * Validate storage provider configuration
 */
export function validateStorageConfig(type: StorageProviderType): string[] {
  const errors: string[] = [];
  
  switch (type) {
    case 'firestore':
      if (!process.env.FIREBASE_PROJECT_ID) {
        errors.push('FIREBASE_PROJECT_ID environment variable is required for Firestore provider');
      }
      if (!process.env.FIREBASE_CLIENT_EMAIL) {
        errors.push('FIREBASE_CLIENT_EMAIL environment variable is required for Firestore provider');
      }
      if (!process.env.FIREBASE_PRIVATE_KEY) {
        errors.push('FIREBASE_PRIVATE_KEY environment variable is required for Firestore provider');
      }
      break;
      
    case 'in-memory':
      // No specific configuration required for in-memory provider
      break;
      
    default:
      errors.push(`Unsupported storage provider type: ${type}`);
  }
  
  return errors;
}

/**
 * Get recommended storage provider based on environment
 */
export function getRecommendedStorageProvider(): StorageProviderType {
  // Check if we're in production
  const isProduction = process.env.NODE_ENV === 'production';
  
  // Check if Firebase is configured
  const hasFirebaseConfig = !!(
    process.env.FIREBASE_PROJECT_ID &&
    process.env.FIREBASE_CLIENT_EMAIL &&
    process.env.FIREBASE_PRIVATE_KEY
  );
  
  if (isProduction && hasFirebaseConfig) {
    return 'firestore';
  }
  
  if (hasFirebaseConfig) {
    return 'firestore';
  }
  
  return 'in-memory';
}

/**
 * Storage provider health check utility
 */
export async function checkStorageProviderHealth(
  provider: DiscountStorageProvider
): Promise<{
  healthy: boolean;
  details: Record<string, unknown>;
}> {
  try {
    const health = await provider.healthCheck();
    
    return {
      healthy: health.status === 'healthy',
      details: {
        status: health.status,
        provider: provider.getConfig().name,
        ...health.details
      }
    };
    
  } catch (error) {
    return {
      healthy: false,
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: provider.getConfig().name
      }
    };
  }
}

// Export singleton factory instance
export const storageProviderFactory = new DefaultStorageProviderFactory();

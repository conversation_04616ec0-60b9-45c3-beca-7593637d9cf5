/**
 * Frontend tRPC Client Setup
 * 
 * This file configures the tRPC client for frontend use with React Query integration,
 * providing type-safe API calls and proper error handling.
 */

import { createTRPCReact } from '@trpc/react-query';
import { createTRPCProxyClient, httpBatchLink, loggerLink, TRPCLink } from '@trpc/client';
import { QueryClient } from '@tanstack/react-query';
import superjson from 'superjson';
import type { AppRouter } from '../server/shared/trpc/router';
import { observable } from '@trpc/server/observable';

/**
 * Create tRPC React hooks
 */
export const trpc = createTRPCReact<AppRouter>();

/**
 * Get base URL for tRPC requests
 */
function getBaseUrl() {
    if (typeof window !== 'undefined') {
        // Browser should use relative URL
        return '';
    }

    if (process.env.VERCEL_URL) {
        // SSR should use Vercel URL
        return `https://${process.env.VERCEL_URL}`;
    }

    // Dev SSR should use localhost
    return `http://localhost:${process.env.PORT ?? 3000}`;
}

/**
 * Custom auth link that handles async authentication with AuthRepository
 */
const authLink: TRPCLink<AppRouter> = () => {
    return ({ next, op }) => {
        return observable((observer) => {
            const getAuthHeaders = async () => {
                if (typeof window === 'undefined') return {};

                try {
                    const { AuthRepository } = await import('../app/repository/AuthRepository');
                    const authRepo = AuthRepository.getInstance();

                    const [userDetails, adminRoles, token] = await Promise.all([
                        authRepo.getUserDetails(),
                        authRepo.getAdminRoles(),
                        authRepo.getValidToken(),
                    ]);

                    const headers: Record<string, string> = {};

                    if (userDetails) {
                        headers['x-user-id'] = userDetails.id.toString();

                        // Determine user role based on admin permissions
                        if (adminRoles?.permissions) {
                            if (adminRoles.permissions.systemAdmin) {
                                headers['x-user-role'] = 'system-admin';
                            } else {
                                headers['x-user-role'] = 'admin';
                            }
                        } else {
                            headers['x-user-role'] = 'user';
                        }
                    }

                    if (token) {
                        headers['Authorization'] = `Bearer ${token}`;
                    }

                    return headers;
                } catch (error) {
                    // eslint-disable-next-line no-console
                    console.error('Failed to get auth headers:', error);
                    return {};
                }
            };

            getAuthHeaders().then((headers) => {
                const newContext = {
                    ...(op.context || {}),
                    headers: {
                        ...(op.context?.headers || {}),
                        ...headers,
                    },
                };

                next({
                    ...op,
                    context: newContext,
                }).subscribe({
                    next: observer.next,
                    error: observer.error,
                    complete: observer.complete,
                });
            }).catch((error) => {
                observer.error(error);
            });
        });
    };
};

/**
 * Create tRPC client configuration
 */
export const trpcClientConfig = {
    links: [
        loggerLink({
            enabled: (opts) =>
                process.env.NODE_ENV === 'development' ||
                (opts.direction === 'down' && opts.result instanceof Error),
        }),
        authLink,
        httpBatchLink({
            url: `${getBaseUrl()}/api/trpc`,
            transformer: superjson,
            headers(opts) {
                // Extract headers from the operation context (set by our auth link)
                // The httpBatchLink headers function receives the operation list
                const contextHeaders = opts.opList?.[0]?.context?.headers || {};

                // Ensure we return a proper HTTPHeaders type
                return contextHeaders as Record<string, string>;
            },
        }),
    ],
};

/**
 * Create vanilla tRPC client (for use outside React components)
 */
export const trpcVanilla = createTRPCProxyClient<AppRouter>(trpcClientConfig);

/**
 * Create React Query client with default configuration
 */
export const createQueryClient = () =>
    new QueryClient({
        defaultOptions: {
            queries: {
                // With SSR, we usually want to set some default staleTime
                // above 0 to avoid refetching immediately on the client
                staleTime: 30 * 1000, // 30 seconds
                retry: (failureCount, error: unknown) => {
                    // Don't retry on 4xx errors (client errors)
                    const errorData = error as { data?: { httpStatus?: number } };
                    if (errorData?.data?.httpStatus && errorData.data.httpStatus >= 400 && errorData.data.httpStatus < 500) {
                        return false;
                    }

                    // Retry up to 3 times for other errors
                    return failureCount < 3;
                },
                refetchOnWindowFocus: false,
            },
            mutations: {
                retry: false, // Don't retry mutations by default
            },
        },
    });

/**
 * Error handling utilities for tRPC
 */
export const handleTRPCError = (error: unknown) => {
    // eslint-disable-next-line no-console
    console.error('tRPC Error:', error);

    const errorData = error as {
        data?: {
            zodError?: {
                fieldErrors?: Record<string, string[]>;
            };
            code?: string;
        };
        message?: string;
    };

    // Extract meaningful error message
    if (errorData?.data?.zodError) {
        // Zod validation error
        const zodError = errorData.data.zodError;
        const fieldErrors = zodError.fieldErrors;

        if (fieldErrors && Object.keys(fieldErrors).length > 0) {
            const firstField = Object.keys(fieldErrors)[0];
            const firstError = fieldErrors[firstField]?.[0];
            return firstError || 'Validation error';
        }

        return 'Invalid input data';
    }

    if (errorData?.message) {
        return errorData.message;
    }

    if (errorData?.data?.code === 'UNAUTHORIZED') {
        return 'Authentication required';
    }

    if (errorData?.data?.code === 'FORBIDDEN') {
        return 'Insufficient permissions';
    }

    if (errorData?.data?.code === 'TOO_MANY_REQUESTS') {
        return 'Too many requests. Please try again later.';
    }

    return 'An unexpected error occurred';
};

/**
 * Authentication helpers that integrate with the existing auth system
 */
export const authHelpers = {
    /**
     * Get current user info from AuthRepository
     */
    getCurrentUser: async () => {
        try {
            // Dynamic import to avoid circular dependencies and ensure client-side only
            const { AuthRepository } = await import('../app/repository/AuthRepository');
            const authRepo = AuthRepository.getInstance();

            const [userDetails, adminRoles] = await Promise.all([
                authRepo.getUserDetails(),
                authRepo.getAdminRoles()
            ]);

            if (!userDetails) {
                return { userId: null, userRole: null, isAdmin: false };
            }

            // Determine user role based on admin permissions
            let userRole = 'user'; // default role
            let isAdmin = false;

            if (adminRoles?.permissions) {
                isAdmin = true;
                // Determine specific admin role based on permissions
                if (adminRoles.permissions.systemAdmin) {
                    userRole = 'system-admin';
                } else {
                    userRole = 'admin';
                }
            }

            return {
                userId: userDetails.id.toString(),
                userRole,
                isAdmin,
                userDetails,
                adminRoles
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('Failed to get current user:', error);
            return { userId: null, userRole: null, isAdmin: false };
        }
    },

    /**
     * Get valid JWT token from AuthRepository
     */
    getValidToken: async () => {
        try {
            const { AuthRepository } = await import('../app/repository/AuthRepository');
            const authRepo = AuthRepository.getInstance();
            return await authRepo.getValidToken();
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('Failed to get valid token:', error);
            return null;
        }
    },

    /**
     * Check if user is authenticated
     */
    isAuthenticated: async () => {
        try {
            const { AuthRepository } = await import('../app/repository/AuthRepository');
            const authRepo = AuthRepository.getInstance();
            return await authRepo.isAuthenticated();
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('Failed to check authentication:', error);
            return false;
        }
    },
};

/**
 * Discount-specific helper functions
 */
export const discountHelpers = {
    /**
     * Calculate discounts with error handling
     */
    calculateDiscounts: async (cartItems: Array<{
        skuId: number;
        variantSkuId?: number;
        quantity: number;
        pricePerUnit: number;
        mrpPerUnit: number;
    }>) => {
        try {
            return await trpcVanilla.discount.calculate.query({ cartItems });
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('Discount calculation failed:', error);

            // Return fallback result
            const originalTotal = cartItems.reduce(
                (sum, item) => sum + (item.pricePerUnit * item.quantity),
                0
            );

            return {
                totalDiscount: 0,
                appliedDiscounts: [],
                originalTotal,
                finalTotal: originalTotal,
                savings: 0,
            };
        }
    },

    /**
     * Get active discounts with error handling
     */
    getActiveDiscounts: async () => {
        try {
            return await trpcVanilla.discount.getActive.query();
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('Failed to get active discounts:', error);
            return [];
        }
    },
};

/**
 * Type exports for use in components
 * Using inferRouterInputs and inferRouterOutputs from tRPC
 */
import type { inferRouterInputs, inferRouterOutputs } from '@trpc/server';

export type RouterInputs = inferRouterInputs<AppRouter>;
export type RouterOutputs = inferRouterOutputs<AppRouter>;
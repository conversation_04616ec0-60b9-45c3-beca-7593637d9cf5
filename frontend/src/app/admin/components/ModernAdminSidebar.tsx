"use client"

import React, { useState } from 'react'
import Image from 'next/image'
import { usePathname, useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { useAdminPermissions } from '../../hooks/useAdminPermissions'
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar
} from "@/components/ui/sidebar"
import { Badge } from "@/components/ui/badge"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
    HomeIcon,
    ShoppingCartIcon,
    ClipboardDocumentListIcon,
    RectangleGroupIcon,
    CubeIcon,
    ArchiveBoxIcon,
    ArrowUturnLeftIcon,
    UserGroupIcon,
    TagIcon
} from '@heroicons/react/24/outline'
import { cn } from '@/lib/utils'
import PermissionWarningModal from './PermissionWarningModal'

interface NavItem {
    name: string
    href: string
    icon: React.ComponentType<{ className?: string }>
    permission?: keyof import('../../types/auth').AdminPermissions
    badge?: string
}

interface ModernAdminSidebarProps {
    isMobileOpen?: boolean
    onCloseMobile?: () => void
}

export function ModernAdminSidebar({ isMobileOpen = false, onCloseMobile }: ModernAdminSidebarProps) {
    const pathname = usePathname()
    const router = useRouter()
    const { t } = useTranslation()
    const { checkPermission, checkPermissionStrict } = useAdminPermissions()
    const { state } = useSidebar()

    // State for permission warning modal
    const [showPermissionWarning, setShowPermissionWarning] = useState(false)
    const [pendingNavigation, setPendingNavigation] = useState<string | null>(null)
    const [pendingPermission, setPendingPermission] = useState<string | null>(null)

    const navigation: NavItem[] = [
        {
            name: t('admin.navigation.dashboard', 'Dashboard'),
            href: '/admin',
            icon: HomeIcon,
        },
        {
            name: t('admin.navigation.orders', 'Orders'),
            href: '/admin/orders',
            icon: ClipboardDocumentListIcon,
            permission: 'viewAllOrders',
        },
        {
            name: t('admin.navigation.carts', 'Carts'),
            href: '/admin/cart',
            icon: ShoppingCartIcon,
            permission: 'viewAllCarts',
        },
        {
            name: t('admin.navigation.categories', 'Categories'),
            href: '/admin/categories',
            icon: RectangleGroupIcon,
            permission: 'manageCategories',
        },
        {
            name: t('admin.navigation.skus', 'SKUs'),
            href: '/admin/skus',
            icon: CubeIcon,
            permission: 'manageSku',
        },
        {
            name: t('admin.navigation.discounts', 'Discounts'),
            href: '/admin/discounts',
            icon: TagIcon,
            permission: 'manageSku', // Using manageSku permission for now, can be updated later
        },
        {
            name: t('admin.navigation.picker', 'Picker'),
            href: '/admin/picker',
            icon: ArchiveBoxIcon,
            permission: 'pickOrder',
        },
        {
            name: t('admin.navigation.returns', 'Returns'),
            href: '/admin/returns',
            icon: ArrowUturnLeftIcon,
            permission: 'returnOrders',
        },
        {
            name: t('admin.navigation.leads', 'Leads'),
            href: '/admin/leads',
            icon: UserGroupIcon,
            permission: 'viewLeads',
        },
    ]

    const isActive = (href: string) => {
        if (href === '/admin') {
            return pathname === '/admin'
        }
        return pathname.startsWith(href)
    }

    const hasPermission = (permission?: keyof import('../../types/auth').AdminPermissions) => {
        if (!permission) return true
        return checkPermission(permission)
    }

    const handleLinkClick = (event: React.MouseEvent, item: NavItem) => {
        // Close mobile sidebar if open
        if (onCloseMobile && isMobileOpen) {
            onCloseMobile()
        }

        // Check if item has permission requirement
        if (item.permission) {
            const hasStrictPermission = checkPermissionStrict(item.permission)

            if (!hasStrictPermission) {
                // Prevent navigation and show warning modal
                event.preventDefault()
                setPendingNavigation(item.href)
                setPendingPermission(item.permission)
                setShowPermissionWarning(true)
                return
            }
        }

        // Allow normal navigation if no permission required or user has permission
        router.push(item.href)
    }

    const handlePermissionWarningContinue = () => {
        if (pendingNavigation) {
            router.push(pendingNavigation)
        }
        setShowPermissionWarning(false)
        setPendingNavigation(null)
        setPendingPermission(null)
    }

    const handlePermissionWarningClose = () => {
        setShowPermissionWarning(false)
        setPendingNavigation(null)
        setPendingPermission(null)
    }

    return (
        <>
            <Sidebar variant="inset" collapsible="icon">
                <SidebarHeader className="border-b border-sidebar-border p-4">
                    <div className="flex items-center gap-2">
                        <Image
                            src="/logo-en.png"
                            alt={t('admin.portal.logoAlt', 'WOW Vandhachu')}
                            width={100}
                            height={100}
                            className=""
                        />
                    </div>
                </SidebarHeader>

                <SidebarContent>
                    <SidebarGroup>
                        <SidebarGroupContent>
                            <SidebarMenu>
                                {navigation.map((item) => {
                                    const active = isActive(item.href)
                                    const permitted = hasPermission(item.permission)
                                    const disabled = !permitted || item.badge === 'Soon'
                                    const IconComponent = item.icon

                                    const menuButton = (
                                        <SidebarMenuButton
                                            asChild={false}
                                            isActive={active}
                                            disabled={disabled}
                                            className={cn(
                                                'w-full justify-start',
                                                disabled && 'opacity-50 cursor-not-allowed'
                                            )}
                                            onClick={(e) => !disabled && handleLinkClick(e, item)}
                                        >
                                            <div className="flex items-center gap-2 w-full">
                                                <IconComponent className="h-4 w-4" />
                                                {state !== "collapsed" && (
                                                    <>
                                                        <span className="flex-1">{item.name}</span>
                                                        {item.badge && (
                                                            <Badge variant="secondary" className="text-xs">
                                                                {item.badge}
                                                            </Badge>
                                                        )}
                                                    </>
                                                )}
                                            </div>
                                        </SidebarMenuButton>
                                    )

                                    return (
                                        <SidebarMenuItem key={item.name}>
                                            {state === "collapsed" ? (
                                                <TooltipProvider>
                                                    <Tooltip>
                                                        <TooltipTrigger asChild>
                                                            {menuButton}
                                                        </TooltipTrigger>
                                                        <TooltipContent side="right" className="flex items-center gap-4">
                                                            {item.name}
                                                            {item.badge && (
                                                                <Badge variant="secondary" className="text-xs">
                                                                    {item.badge}
                                                                </Badge>
                                                            )}
                                                        </TooltipContent>
                                                    </Tooltip>
                                                </TooltipProvider>
                                            ) : (
                                                menuButton
                                            )}
                                        </SidebarMenuItem>
                                    )
                                })}
                            </SidebarMenu>
                        </SidebarGroupContent>
                    </SidebarGroup>
                </SidebarContent>

                <SidebarFooter className="border-t border-sidebar-border p-4">
                    {state !== "collapsed" && (
                        <div className="text-xs text-muted-foreground">
                            <div>{t('admin.portal.version', 'Version')} {process.env.NEXT_PUBLIC_VERSION || '1.0.0'}</div>
                            <div className="mt-1">{t('admin.portal.environment', process.env.NEXT_PUBLIC_APP_MODE || 'admin')}</div>
                        </div>
                    )}
                </SidebarFooter>
            </Sidebar>

            {/* Permission Warning Modal */}
            <PermissionWarningModal
                isOpen={showPermissionWarning}
                onClose={handlePermissionWarningClose}
                onContinue={handlePermissionWarningContinue}
                permissionName={pendingPermission || undefined}
            />
        </>
    )
} 
"use client";

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { trpc } from '@/lib/trpc-client';
import { toast } from 'react-toastify';
import { CreateDiscountInput } from '@/server/features/discount/discount.validation';
import { useDiscountStore } from '@/app/hooks/useDiscountStore';
import DiscountForm from '@/app/admin/components/forms/DiscountForm';

export default function CreateDiscountPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Zustand store
  const { setLoading, setError } = useDiscountStore();

  // Create mutation
  const createMutation = trpc.discount.create.useMutation({
    onSuccess: () => {
      toast.success('Discount created successfully');
      setError(null);
      router.push('/admin/discounts');
    },
    onError: (error) => {
      const errorMessage = `Failed to create discount: ${error.message}`;
      toast.error(errorMessage);
      setError(errorMessage);
      setIsSubmitting(false);
      setLoading(false);
    },
  });

  // Memoized handlers
  const handleCancel = useCallback(() => {
    router.push('/admin/discounts');
  }, [router]);

  const handleSubmit = useCallback(async (data: CreateDiscountInput) => {
    setIsSubmitting(true);
    setLoading(true);
    setError(null);

    try {
      await createMutation.mutateAsync(data);
    } catch (error) {
      // Error is handled by the mutation's onError callback
      console.error('Discount creation failed:', error);
    } finally {
      setIsSubmitting(false);
      setLoading(false);
    }
  }, [setLoading, setError, createMutation.mutateAsync]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeftIcon className="h-4 w-4" />
          Back to Discounts
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-foreground">Create Discount</h1>
          <p className="text-muted-foreground">
            Create a new discount rule for your customers
          </p>
        </div>
      </div>

      {/* Discount Form */}
      <DiscountForm
        mode="create"
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isSubmitting={isSubmitting}
      />
    </div>
  );
}
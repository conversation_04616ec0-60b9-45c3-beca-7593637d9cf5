"use client";

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PlusIcon, MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline';
import DataTable, { Column } from '../components/DataTable';
import StatusBadge from '../components/StatusBadge';
import { trpc } from '@/lib/trpc-client';
import { toast } from 'react-toastify';
import { DiscountResponse } from '@/server/features/discount/discount.validation';
import { useDiscountStore } from '@/app/hooks/useDiscountStore';
import DeleteConfirmationModal from '@/app/admin/components/DeleteConfirmationModal';

export default function DiscountsPage() {
  const router = useRouter();

  // Zustand store
  const {
    filters,
    pagination,
    updateFilter,
    setPage,
    updatePaginationFromResponse,
    setLoading,
  } = useDiscountStore();

  // Delete confirmation modal state
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [discountToDelete, setDiscountToDelete] = useState<DiscountResponse | null>(null);

  // Memoized query parameters
  const queryParams = useMemo(() => ({
    page: pagination.page,
    limit: pagination.limit,
    filters: {
      isActive: filters.isActive,
      search: filters.search || undefined,
    }
  }), [pagination.page, pagination.limit, filters.isActive, filters.search]);

  // tRPC queries
  const {
    data: discountsData,
    isLoading,
    refetch
  } = trpc.discount.list.useQuery(queryParams);

  // Update store when data changes
  useEffect(() => {
    if (discountsData) {
      updatePaginationFromResponse(discountsData);
    }
  }, [discountsData, updatePaginationFromResponse]);

  // Update loading state
  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading, setLoading]);

  // Memoized handlers
  const handleCreateDiscount = useCallback(() => {
    router.push('/admin/discounts/create');
  }, [router]);

  const handleSearchChange = useCallback((value: string) => {
    updateFilter('search', value || undefined);
  }, [updateFilter]);

  const handleStatusFilterChange = useCallback((value: string) => {
    updateFilter('isActive', value === 'all' ? undefined : value === 'active');
  }, [updateFilter]);

  const handlePageChange = useCallback((page: number) => {
    setPage(page);
  }, [setPage]);

  // Delete mutation
  const deleteMutation = trpc.discount.delete.useMutation({
    onSuccess: () => {
      toast.success('Discount deleted successfully');
      refetch();
    },
    onError: (error) => {
      toast.error(`Failed to delete discount: ${error.message}`);
    }
  });

  const handleEditDiscount = (discount: DiscountResponse, event?: React.MouseEvent) => {
    // Prevent event bubbling when clicking edit button (though this is redundant since it does the same action as row click)
    event?.stopPropagation();
    router.push(`/admin/discounts/${discount.id}/edit`);
  };

  const handleDeleteDiscount = useCallback((discount: DiscountResponse, event?: React.MouseEvent) => {
    // Prevent event bubbling to avoid triggering row click (edit navigation)
    event?.stopPropagation();
    setDiscountToDelete(discount);
    setShowDeleteModal(true);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (!discountToDelete) return;

    try {
      await deleteMutation.mutateAsync({ id: discountToDelete.id });
      // Close modal on success
      setShowDeleteModal(false);
      setDiscountToDelete(null);
    } catch {
      // Error is handled by the mutation's onError callback
      // Keep modal open to show error state
    }
  }, [discountToDelete, deleteMutation]);

  const handleCloseDeleteModal = useCallback(() => {
    if (!deleteMutation.isPending) {
      setShowDeleteModal(false);
      setDiscountToDelete(null);
    }
  }, [deleteMutation.isPending]);

  // Memoized utility functions
  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }, []);

  const formatDate = useCallback((date: Date) => {
    return new Intl.DateTimeFormat('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(new Date(date));
  }, []);

  const isDiscountExpired = useCallback((validTo: Date) => {
    return new Date(validTo) < new Date();
  }, []);

  const getDiscountStatus = useCallback((discount: DiscountResponse) => {
    if (!discount.isActive) return 'inactive';
    if (isDiscountExpired(discount.validTo)) return 'inactive';
    return 'active';
  }, [isDiscountExpired]);

  // Memoized computed values
  const filteredDiscounts = useMemo(() => discountsData?.items || [], [discountsData?.items]);
  const totalCount = useMemo(() => discountsData?.total || 0, [discountsData?.total]);

  // Derived values for display
  const statusFilterValue = useMemo(() => {
    if (filters.isActive === undefined) return 'all';
    return filters.isActive ? 'active' : 'inactive';
  }, [filters.isActive]);

  const searchValue = useMemo(() => filters.search || '', [filters.search]);

  const columns: Column<DiscountResponse>[] = [
    {
      key: 'name',
      header: 'Discount Name',
      sortable: true,
      render: (discount) => (
        <div className="flex flex-col">
          <span className="font-medium text-foreground">{discount.name}</span>
          {discount.description && (
            <span className="text-sm text-muted-foreground">{discount.description}</span>
          )}
        </div>
      ),
    },
    {
      key: 'type',
      header: 'Type',
      render: (discount) => (
        <div className="flex flex-col">
          <Badge variant="outline" className="w-fit">
            {discount.percentage}% Off
          </Badge>
          <span className="text-xs text-muted-foreground mt-1">
            Cap: {formatCurrency(discount.maxDiscountAmount || 0)}
          </span>
        </div>
      ),
    },
    {
      key: 'minCartValue',
      header: 'Min Cart Value',
      sortable: true,
      render: (discount) => formatCurrency(discount.minCartValue || 0),
      getSortValue: (discount) => discount.minCartValue || 0,
    },
    {
      key: 'validFrom',
      header: 'Valid Period',
      sortable: true,
      render: (discount) => (
        <div className="flex flex-col text-sm">
          <span>From: {formatDate(discount.validFrom)}</span>
          <span>To: {formatDate(discount.validTo)}</span>
        </div>
      ),
      getSortValue: (discount) => new Date(discount.validFrom).getTime(),
    },
    {
      key: 'usageCount',
      header: 'Usage',
      sortable: true,
      render: (discount) => (
        <div className="flex flex-col text-sm">
          <span className="font-medium">{discount.usageCount}</span>
          {discount.maxUsage && (
            <span className="text-muted-foreground">
              / {discount.maxUsage} max
            </span>
          )}
        </div>
      ),
    },
    {
      key: 'isActive',
      header: 'Status',
      render: (discount) => (
        <StatusBadge status={getDiscountStatus(discount)} />
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (discount) => (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={(event) => handleEditDiscount(discount, event)}
          >
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={(event) => handleDeleteDiscount(discount, event)}
            disabled={deleteMutation.isPending}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Discounts</h1>
          <p className="text-muted-foreground">
            Manage discount rules and promotional offers
          </p>
        </div>
        <Button onClick={handleCreateDiscount} className="w-fit">
          <PlusIcon className="h-4 w-4 mr-2" />
          Create Discount
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Discounts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Active Discounts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-600">
              {filteredDiscounts.filter((d: DiscountResponse) => getDiscountStatus(d) === 'active').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredDiscounts.reduce((sum: number, d: DiscountResponse) => sum + d.usageCount, 0)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Expired
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {filteredDiscounts.filter((d: DiscountResponse) => isDiscountExpired(d.validTo)).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
          <CardDescription>
            Filter and search through your discount rules
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search discounts..."
                  value={searchValue}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-full sm:w-48">
              <Select value={statusFilterValue} onValueChange={handleStatusFilterChange}>
                <SelectTrigger>
                  <FunnelIcon className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <DataTable
        data={filteredDiscounts}
        columns={columns}
        loading={isLoading}
        emptyMessage="No discounts found. Create your first discount to get started."
        onRowClick={handleEditDiscount}
        getItemId={(discount) => discount.id}
      />

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, totalCount)} of {totalCount} discounts
          </p>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(Math.max(1, pagination.page - 1))}
              disabled={pagination.page === 1}
            >
              Previous
            </Button>
            <span className="text-sm text-muted-foreground">
              Page {pagination.page} of {pagination.totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(Math.min(pagination.totalPages, pagination.page + 1))}
              disabled={pagination.page === pagination.totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={showDeleteModal}
        onClose={handleCloseDeleteModal}
        onConfirm={handleConfirmDelete}
        title="Delete Discount"
        itemName={discountToDelete?.name}
        isLoading={deleteMutation.isPending}
      />
    </div>
  );
}
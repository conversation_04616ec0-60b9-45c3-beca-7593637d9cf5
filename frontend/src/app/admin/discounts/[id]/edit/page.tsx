"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { trpc } from '@/lib/trpc-client';
import { toast } from 'react-toastify';
import { UpdateDiscountInput, DiscountResponse } from '@/server/features/discount/discount.validation';
import { useDiscountStore } from '@/app/hooks/useDiscountStore';
import DiscountForm from '@/app/admin/components/forms/DiscountForm';

export default function EditDiscountPage() {
  const router = useRouter();
  const params = useParams();
  const discountId = params.id as string;
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [discount, setDiscount] = useState<DiscountResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Zustand store
  const { setLoading: setStoreLoading, setError: setStoreError } = useDiscountStore();

  // Fetch discount data
  const { data: discountData, isLoading: isFetching, error: fetchError } = trpc.discount.getById.useQuery(
    { id: discountId },
    {
      enabled: !!discountId,
      retry: false,
    }
  );

  // Update mutation
  const updateMutation = trpc.discount.update.useMutation({
    onSuccess: () => {
      toast.success('Discount updated successfully');
      setStoreError(null);
      router.push('/admin/discounts');
    },
    onError: (error) => {
      const errorMessage = `Failed to update discount: ${error.message}`;
      toast.error(errorMessage);
      setStoreError(errorMessage);
      setIsSubmitting(false);
      setStoreLoading(false);
    },
  });

  // Handle data loading
  useEffect(() => {
    if (isFetching) {
      setIsLoading(true);
      setError(null);
    } else if (fetchError) {
      setIsLoading(false);
      setError(fetchError.message);
    } else if (discountData) {
      setDiscount(discountData);
      setIsLoading(false);
      setError(null);
    } else {
      setIsLoading(false);
      setError('Discount not found');
    }
  }, [discountData, isFetching, fetchError]);

  // Memoized handlers
  const handleCancel = useCallback(() => {
    router.push('/admin/discounts');
  }, [router]);

  const handleSubmit = useCallback(async (data: UpdateDiscountInput) => {
    setIsSubmitting(true);
    setStoreLoading(true);
    setStoreError(null);
    
    try {
      await updateMutation.mutateAsync(data);
    } catch (error) {
      // Error is handled by the mutation's onError callback
      console.error('Discount update failed:', error);
    } finally {
      setIsSubmitting(false);
      setStoreLoading(false);
    }
  }, [setStoreLoading, setStoreError, updateMutation.mutateAsync]);

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <ArrowLeftIcon className="h-4 w-4" />
            Back to Discounts
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Edit Discount</h1>
            <p className="text-muted-foreground">Loading discount data...</p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <ArrowLeftIcon className="h-4 w-4" />
            Back to Discounts
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Edit Discount</h1>
            <p className="text-muted-foreground">Error loading discount</p>
          </div>
        </div>
        <div className="flex flex-col items-center justify-center py-12 space-y-4">
          <div className="text-red-500 text-center">
            <h3 className="text-lg font-medium">Failed to load discount</h3>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
          </div>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // Main render
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeftIcon className="h-4 w-4" />
          Back to Discounts
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-foreground">Edit Discount</h1>
          <p className="text-muted-foreground">
            Update discount: {discount?.name}
          </p>
        </div>
      </div>

      {/* Discount Form */}
      {discount && (
        <DiscountForm
          mode="edit"
          initialData={discount}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isSubmitting}
        />
      )}
    </div>
  );
}

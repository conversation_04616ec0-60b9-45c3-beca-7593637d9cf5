import { create } from 'zustand';
import { Discount, DiscountType } from '@/server/features/discount/discount.types';

// Discount filters interface matching the tRPC schema
interface DiscountFilters {
  isActive?: boolean;
  type?: DiscountType;
  validAt?: Date;
  search?: string;
}

// Pagination state
interface PaginationState {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// UI state
interface UIState {
  isLoading: boolean;
  error: string | null;
  viewMode: 'table' | 'cards';
  isExporting: boolean;
}

// Main store interface
interface DiscountStore {
  // State
  selectedDiscounts: Discount[];
  filters: DiscountFilters;
  pagination: PaginationState;
  ui: UIState;

  // Filter actions
  setFilters: (filters: Partial<DiscountFilters>) => void;
  updateFilter: (key: keyof DiscountFilters, value: any) => void;
  clearFilters: () => void;

  // Pagination actions
  setPagination: (pagination: Partial<PaginationState>) => void;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  updatePaginationFromResponse: (response: { total: number; page: number; limit: number; totalPages: number }) => void;

  // Selection actions
  setSelectedDiscounts: (discounts: Discount[]) => void;
  addSelectedDiscount: (discount: Discount) => void;
  removeSelectedDiscount: (discountId: string) => void;
  clearSelectedDiscounts: () => void;
  toggleDiscountSelection: (discount: Discount) => void;
  selectAllDiscounts: (discounts: Discount[]) => void;
  isDiscountSelected: (discountId: string) => boolean;

  // UI actions
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  setViewMode: (mode: 'table' | 'cards') => void;
  setIsExporting: (isExporting: boolean) => void;

  // Computed getters
  getSelectedCount: () => number;
  hasFiltersApplied: () => boolean;
  getFilterSummary: () => string;
}

// Default states
const defaultFilters: DiscountFilters = {
  isActive: undefined,
  type: undefined,
  validAt: undefined,
  search: undefined,
};

const defaultPagination: PaginationState = {
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0,
};

const defaultUIState: UIState = {
  isLoading: false,
  error: null,
  viewMode: 'table',
  isExporting: false,
};

export const useDiscountStore = create<DiscountStore>((set, get) => ({
  // Initial state
  selectedDiscounts: [],
  filters: defaultFilters,
  pagination: defaultPagination,
  ui: defaultUIState,

  // Filter actions
  setFilters: (newFilters: Partial<DiscountFilters>) => {
    set((state) => ({
      filters: { ...state.filters, ...newFilters },
      pagination: { ...state.pagination, page: 1 }, // Reset to first page when filters change
    }));
  },

  updateFilter: (key: keyof DiscountFilters, value: any) => {
    set((state) => ({
      filters: { ...state.filters, [key]: value },
      pagination: { ...state.pagination, page: 1 }, // Reset to first page when filter changes
    }));
  },

  clearFilters: () => {
    set((state) => ({
      filters: defaultFilters,
      pagination: { ...state.pagination, page: 1 },
    }));
  },

  // Pagination actions
  setPagination: (newPagination: Partial<PaginationState>) => {
    set((state) => ({
      pagination: { ...state.pagination, ...newPagination },
    }));
  },

  setPage: (page: number) => {
    set((state) => ({
      pagination: { ...state.pagination, page },
    }));
  },

  setLimit: (limit: number) => {
    set((state) => ({
      pagination: { ...state.pagination, limit, page: 1 }, // Reset to first page when limit changes
    }));
  },

  updatePaginationFromResponse: (response: { total: number; page: number; limit: number; totalPages: number }) => {
    set((state) => ({
      pagination: {
        ...state.pagination,
        total: response.total,
        page: response.page,
        limit: response.limit,
        totalPages: response.totalPages,
      },
    }));
  },

  // Selection actions
  setSelectedDiscounts: (discounts: Discount[]) => {
    set({ selectedDiscounts: discounts });
  },

  addSelectedDiscount: (discount: Discount) => {
    const { selectedDiscounts } = get();
    const isAlreadySelected = selectedDiscounts.some(selected => selected.id === discount.id);

    if (!isAlreadySelected) {
      set({ selectedDiscounts: [...selectedDiscounts, discount] });
    }
  },

  removeSelectedDiscount: (discountId: string) => {
    const { selectedDiscounts } = get();
    set({
      selectedDiscounts: selectedDiscounts.filter(discount => discount.id !== discountId)
    });
  },

  clearSelectedDiscounts: () => {
    set({ selectedDiscounts: [] });
  },

  toggleDiscountSelection: (discount: Discount) => {
    const { selectedDiscounts } = get();
    const isSelected = selectedDiscounts.some(selected => selected.id === discount.id);

    if (isSelected) {
      set({
        selectedDiscounts: selectedDiscounts.filter(selected => selected.id !== discount.id)
      });
    } else {
      set({
        selectedDiscounts: [...selectedDiscounts, discount]
      });
    }
  },

  selectAllDiscounts: (discounts: Discount[]) => {
    set({ selectedDiscounts: [...discounts] });
  },

  isDiscountSelected: (discountId: string) => {
    const { selectedDiscounts } = get();
    return selectedDiscounts.some(discount => discount.id === discountId);
  },

  // UI actions
  setLoading: (isLoading: boolean) => {
    set((state) => ({
      ui: { ...state.ui, isLoading }
    }));
  },

  setError: (error: string | null) => {
    set((state) => ({
      ui: { ...state.ui, error }
    }));
  },

  setViewMode: (mode: 'table' | 'cards') => {
    set((state) => ({
      ui: { ...state.ui, viewMode: mode }
    }));
  },

  setIsExporting: (isExporting: boolean) => {
    set((state) => ({
      ui: { ...state.ui, isExporting }
    }));
  },

  // Computed getters
  getSelectedCount: () => {
    const { selectedDiscounts } = get();
    return selectedDiscounts.length;
  },

  hasFiltersApplied: () => {
    const { filters } = get();
    return Object.values(filters).some(value => value !== undefined && value !== '');
  },

  getFilterSummary: () => {
    const { filters } = get();
    const appliedFilters: string[] = [];

    if (filters.isActive !== undefined) {
      appliedFilters.push(`Status: ${filters.isActive ? 'Active' : 'Inactive'}`);
    }
    if (filters.type) {
      appliedFilters.push(`Type: ${filters.type}`);
    }
    if (filters.search) {
      appliedFilters.push(`Search: "${filters.search}"`);
    }
    if (filters.validAt) {
      appliedFilters.push(`Valid at: ${filters.validAt.toLocaleDateString()}`);
    }

    return appliedFilters.length > 0 ? appliedFilters.join(', ') : 'No filters applied';
  },
}));

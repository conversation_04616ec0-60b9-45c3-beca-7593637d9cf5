'use client';

import { useState, useMemo, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import { SessionManager } from './SessionManager';
import { LanguageSwitcher } from './LanguageSwitcher';
import { useSession } from '../../context/SessionContext';
import { useAuth } from '../../context/AuthContext';
import LoginModal from '../LoginModal';
import SearchBar from '../SearchBar';
import Link from 'next/link';
import CartDrawer from '../CartDrawer';
import MobileBottomNav from './MobileBottomNav';
import MobileMoreMenu from './MobileMoreMenu';
import MobileSessionsSheet from './MobileSessionsSheet';
import MobileAccountSheet from './MobileAccountSheet';
import { isCustomerFacingMode } from '../../../lib/utils';
import CategoriesDrawer from '../CategoriesDrawer/CategoriesDrawer';
import Image from 'next/image';

interface HeaderProps {
    isSidebarCollapsed?: boolean;
}

const LOGO_BAR_HEIGHT = 60; // Approximate height of the logo bar in pixels, adjust as needed

export function Header({ isSidebarCollapsed = false }: HeaderProps) {
    const { t, i18n } = useTranslation();
    const router = useRouter();
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isCartDrawerOpen, setIsCartDrawerOpen] = useState(false);
    const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
    const userMenuRef = useRef<HTMLDivElement>(null);

    // New state for mobile UI elements
    const [isMoreMenuOpen, setIsMoreMenuOpen] = useState(false);
    const [isSessionsSheetOpen, setIsSessionsSheetOpen] = useState(false);
    const [isAccountSheetOpen, setIsAccountSheetOpen] = useState(false);
    const [isCategoriesDrawerOpen, setIsCategoriesDrawerOpen] = useState(false);

    // State for sticky header mobile
    const [isSearchSticky, setIsSearchSticky] = useState(false);
    const [logoTranslateY, setLogoTranslateY] = useState(0);
    const [logoOpacity, setLogoOpacity] = useState(1); // New state for logo opacity

    const customerFacing = useMemo(() => isCustomerFacingMode(), []);

    const {
        sessions,
        activeSessionId,
        setActiveSessionId,
        addSession,
        removeSession,
        updateSession,
        cartDetailsQuery,
        updateCartItemQuantityMutation,
        validateCartMutation,
        cartNotifications,
        dismissCartNotification,
        hasActiveCustomerSession
    } = useSession();

    // Auth context for User (Bhuvanesh) Login
    const {
        isUserLoggedIn,
        userDetails,
        userLogout
    } = useAuth();

    // Auto-show login modal for admin mode when not logged in
    useEffect(() => {
        if (!customerFacing && !isUserLoggedIn) {
            setIsLoginModalOpen(true);
        }
    }, [customerFacing, isUserLoggedIn]);

    // Handle click outside to close user account dropdown
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
                setIsMenuOpen(false);
            }
        };

        // Only add listener when dropdown is open
        if (isMenuOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        // Cleanup function to remove event listener
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isMenuOpen]);

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen);
    };

    const handleAccountClick = () => {
        setIsMenuOpen(false);
        router.push('/account');
    };

    // Map sessions to the format expected by SessionManager
    const sessionTabs = sessions.map(session => {
        let count = 0;
        const s = sessions.find(sc => sc.id === session.id);
        if (s) count = s.cartItems.reduce((total, item) => total + item.quantity, 0);

        return {
            id: session.id,
            customerName: session.customerName,
            customerPhone: session.customerPhone,
            lastActive: session.lastActive,
            cartItemsCount: count,
            location: session.location
        };
    });

    const handleSessionSelect = (sessionId: string) => {
        setActiveSessionId(sessionId);
    };

    const handleNewSession = async (sessionData: { customerName: string; customerPhone?: string; location?: { lat: number; lng: number } }) => {
        try {
            const newSessionId = await addSession({
                ...sessionData
            });
            setActiveSessionId(newSessionId);
        } catch (error) {
            console.error('Header: Failed to create new session:', error);
        }
    };

    const handleUpdateSession = (sessionId: string, data: { customerName: string; customerPhone?: string; location?: { lat: number; lng: number } }) => {
        updateSession(sessionId, {
            ...data
        });
    };

    const handleCloseSession = (sessionId: string) => {
        removeSession(sessionId);
    };

    const activeCartItemCount = cartDetailsQuery.data?.totalCartQuantity || 0;

    // Calculate cart total price considering discounts
    const cartData = cartDetailsQuery.data;
    const activeCartTotalPrice = useMemo(() => {
        if (!cartData) return 0;

        // Use discounted final total if available, otherwise use regular total
        const discountInfo = cartData.discountInfo;
        if (discountInfo && discountInfo.totalDiscount > 0) {
            return discountInfo.finalTotal;
        }

        return cartData.itemsTotalPrice;
    }, [cartData]);

    // Get active session details for display
    const activeSessionDetails = sessions.find(s => s.id === activeSessionId);
    const activeSessionDisplayName = activeSessionDetails
        ? activeSessionDetails.customerName || `${t('session.session', 'Session')} ${sessions.findIndex(s => s.id === activeSessionId) + 1}`
        : null;

    const handleCartIconClick = () => {
        setIsCartDrawerOpen(true);
    };

    // Placeholder handlers for new mobile sheets - to be implemented
    const toggleMoreMenu = () => setIsMoreMenuOpen(!isMoreMenuOpen);
    const openSessionsSheet = () => setIsSessionsSheetOpen(true);
    const closeSessionsSheet = () => setIsSessionsSheetOpen(false);
    const openAccountSheet = () => setIsAccountSheetOpen(true);
    const closeAccountSheet = () => setIsAccountSheetOpen(false);

    // New click handlers for bottom nav
    const handleCategoriesClick = () => {
        setIsCategoriesDrawerOpen(true);
    };

    const handleHomeClick = () => {
        router.push('/');
    };

    const handleSelectCategory = (slug: string) => {
        console.log('Selected category slug from Header:', slug);
        router.push(`/categories/${slug}`);
        if (isCategoriesDrawerOpen) {
            toggleCategoriesDrawer();
        }
    };

    // Prefetch category data for faster navigation
    const prefetchCategory = (categoryId: string) => {
        router.prefetch(`/categories/${categoryId}`);
    };

    const toggleCategoriesDrawer = () => setIsCategoriesDrawerOpen(!isCategoriesDrawerOpen);

    // Sticky header logic for mobile
    useEffect(() => {
        let lastScrollY = window.scrollY;
        let ticking = false;

        const handleScroll = () => {
            const currentScrollY = window.scrollY;
            if (!ticking) {
                window.requestAnimationFrame(() => {
                    if (window.innerWidth < 768) { // Only apply for mobile
                        const scrollProgress = Math.min(1, Math.max(0, currentScrollY / LOGO_BAR_HEIGHT));

                        setLogoTranslateY(-scrollProgress * LOGO_BAR_HEIGHT);
                        setLogoOpacity(1 - scrollProgress);

                        if (currentScrollY > LOGO_BAR_HEIGHT) {
                            setIsSearchSticky(true);
                        } else {
                            setIsSearchSticky(false);
                        }
                    } else { // On desktop, ensure these states are reset
                        setLogoTranslateY(0);
                        setLogoOpacity(1);
                        setIsSearchSticky(false);
                    }
                    lastScrollY = currentScrollY;
                    ticking = false;
                });
                ticking = true;
            }
        };

        window.addEventListener('scroll', handleScroll, { passive: true });
        handleScroll(); // Initial check

        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    return (
        <>
            <CategoriesDrawer
                isOpen={isCategoriesDrawerOpen}
                onClose={toggleCategoriesDrawer}
                onSelectCategory={handleSelectCategory}
                onPrefetchCategory={prefetchCategory}
            />
            {/* ---- Mobile Header ---- */}
            <div className="md:hidden bg-white shadow-md" style={{ height: `${LOGO_BAR_HEIGHT + 60}px` }}>
                {/* Logo Bar - This part will scroll up and fade */}
                <div
                    className="px-4 py-3 flex items-center justify-between transition-all duration-200 ease-out"
                    style={{
                        transform: `translateY(${logoTranslateY}px)`,
                        opacity: logoOpacity,
                        height: `${LOGO_BAR_HEIGHT}px`,
                        willChange: 'transform, opacity'
                    }}
                >
                    {/* Mobile Categories Icon */}
                    <button
                        onClick={toggleCategoriesDrawer}
                        className="p-2 rounded-md text-gray-600 hover:bg-gray-100 hover:text-gray-800 mr-2"
                        aria-label={t('header.categories.ariaLabel', 'Open categories menu')}
                    >
                        {/* Categories Icon SVG for Mobile */}
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                            <path strokeLinecap="round" strokeLinejoin="round" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                        </svg>
                    </button>

                    <Link href="/" className="flex items-center space-x-2 flex-shrink-0">
                        <Image
                            src={i18n.language === 'ta' ? '/logo-ta.png' : '/logo-en.png'}
                            alt={i18n.language === 'ta' ? 'வாவ் வந்தாச்சு' : 'WOW vandhaachu'}
                            width={200}
                            height={60}
                            className="h-12 w-auto"
                            priority
                        />
                    </Link>
                    {hasActiveCustomerSession && activeSessionDisplayName && !customerFacing && (
                        <span className="ml-2 text-sm font-medium text-gray-700 truncate">
                            ({activeSessionDisplayName})
                        </span>
                    )}
                    {!(hasActiveCustomerSession && activeSessionDisplayName && !customerFacing) && <div className="flex-grow"></div>}
                    <button onClick={toggleMoreMenu} className="p-2 rounded-md text-gray-600 hover:bg-gray-100 hover:text-gray-800 ml-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                            <path strokeLinecap="round" strokeLinejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <span className="sr-only">{t('header.moreOptions', 'More options')}</span>
                    </button>
                </div>
                {/* Search Bar Container - This part will become sticky */}
                <div
                    className={`py-2 bg-white w-full transition-shadow duration-200 ease-out ${isSearchSticky ? 'fixed top-0 left-0 right-0 shadow-md z-40' : 'relative z-10'}`}
                    style={{ willChange: 'box-shadow' }}
                >
                    <SearchBar />
                </div>
            </div>

            {/* ---- Desktop Header ---- */}
            <header className="hidden md:flex bg-white shadow-md items-center sticky top-0 z-40">
                <div className="px-4 py-3 flex items-center justify-between w-full">


                    <Link href="/" className="flex items-center space-x-2 flex-shrink-0">
                        <Image
                            src={i18n.language === 'ta' ? '/logo-ta.png' : '/logo-en.png'}
                            alt={i18n.language === 'ta' ? 'வாவ் வந்தாச்சு' : 'WOW vandhaachu'}
                            width={200}
                            height={60}
                            className="h-12 w-auto"
                            priority
                        />
                    </Link>
                    <div className="flex-grow justify-center px-4">
                        <div className="w-full max-w-xl mx-auto">
                            <SearchBar />
                        </div>
                    </div>
                    <div className="flex items-center space-x-4">
                        <LanguageSwitcher />
                        {!customerFacing && (
                            <div className="relative" ref={userMenuRef}>
                                <button
                                    onClick={() => isUserLoggedIn ? toggleMenu() : setIsLoginModalOpen(true)}
                                    className="flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-md transition-colors"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                                    </svg>
                                    <span>{isUserLoggedIn ? (userDetails?.name || t('header.account', 'Account')) : t('header.login', 'Login')}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 transition-transform ${isMenuOpen ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                </button>
                                {isMenuOpen && (
                                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-20 py-1">
                                        {isUserLoggedIn ? (
                                            <>
                                                <button className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100" onClick={handleAccountClick}>{t('header.accountMenu.account', 'Account')}</button>
                                                <button className="block w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 hover:text-red-700" onClick={() => { userLogout(); setIsMenuOpen(false); }}>{t('header.logout', 'Logout')}</button>
                                            </>
                                        ) : (<></>)}
                                    </div>
                                )}
                            </div>
                        )}
                        <button
                            onClick={handleCartIconClick}
                            className="flex items-center space-x-2 bg-[var(--color-green-600)] hover:brightness-90 text-white px-4 py-2 rounded-md shadow-sm transition-colors duration-150 ease-in-out"
                            aria-label={t('header.cart.ariaLabel', 'Open cart')}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            {cartDetailsQuery.isLoading ? (
                                <div className="flex items-center">
                                    <svg className="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span className="text-sm font-medium">{t('header.cart.loading', 'Loading...')}</span>
                                </div>
                            ) : activeCartItemCount > 0 ? (
                                <div className="flex flex-col items-start">
                                    <div className="flex items-center">
                                        <span className="text-sm font-medium">
                                            {activeCartItemCount} {activeCartItemCount === 1 ? t('header.cart.item', 'item') : t('header.cart.items', 'items')}
                                        </span>
                                        {cartData?.discountInfo && cartData.discountInfo.totalDiscount > 0 && (
                                            <span className="ml-1 bg-yellow-400 text-yellow-900 px-1.5 py-0.5 rounded-full text-xs font-medium">
                                                {t('header.cart.discount', 'DISCOUNT')}
                                            </span>
                                        )}
                                    </div>
                                    <div className="flex items-center space-x-1">
                                        {cartData?.discountInfo && cartData.discountInfo.totalDiscount > 0 ? (
                                            <>
                                                <span className="text-xs text-gray-300 line-through">₹{cartData.itemsTotalPrice}</span>
                                                <span className="text-sm font-semibold">₹{activeCartTotalPrice}</span>
                                            </>
                                        ) : (
                                            <span className="text-sm font-semibold">₹{activeCartTotalPrice}</span>
                                        )}
                                    </div>
                                </div>
                            ) : (
                                <span className="text-sm font-medium">{t('header.cart.myCart', 'My Cart')}</span>
                            )}
                        </button>
                    </div>
                </div>
            </header>

            {/* Session Manager (Chrome-like tabs) - Desktop only */}
            {!customerFacing && (
                <div className={`hidden md:block bg-gray-100 border-b border-gray-200 sticky top-[68px] z-30 transition-all duration-300 ease-in-out ${isSidebarCollapsed ? 'md:ml-16 md:w-[calc(100vw-64px)]' : 'md:ml-64 md:w-[calc(100vw-256px)]'
                    }`}>
                    <div className="px-4">
                        <SessionManager
                            tabs={sessionTabs}
                            activeTabId={activeSessionId}
                            onTabSelect={handleSessionSelect}
                            onNewTab={handleNewSession}
                            onCloseTab={handleCloseSession}
                            onUpdateTab={handleUpdateSession}
                        />
                    </div>
                </div>
            )}

            {/* Mobile Bottom Navigation */}
            <MobileBottomNav
                onCategoriesClick={handleCategoriesClick}
                onHomeClick={handleHomeClick}
                onSessionsClick={openSessionsSheet}
                onCartClick={handleCartIconClick}
                cartItemCount={activeCartItemCount}
                showSessionsIcon={!customerFacing}
            />

            {/* Mobile "More" Menu */}
            <MobileMoreMenu
                isOpen={isMoreMenuOpen}
                onClose={toggleMoreMenu}
                onAccountClick={openAccountSheet}
                isUserLoggedIn={isUserLoggedIn}
                userName={userDetails?.name}
                onUserLogout={() => { userLogout(); toggleMoreMenu(); }}
                onUserLogin={() => { setIsLoginModalOpen(true); toggleMoreMenu(); }}
                isCustomerFacingMode={customerFacing}
            />

            <MobileSessionsSheet
                isOpen={isSessionsSheetOpen}
                onClose={closeSessionsSheet}
                sessions={sessionTabs}
                activeSessionId={activeSessionId}
                onTabSelect={(id: string) => { setActiveSessionId(id); closeSessionsSheet(); }}
                onNewTab={(data: { customerName: string; customerPhone?: string; location?: { lat: number; lng: number } }) => { handleNewSession(data); }}
                onUpdateTab={handleUpdateSession}
                onCloseTab={handleCloseSession}
            />
            <MobileAccountSheet
                isOpen={isAccountSheetOpen}
                onClose={closeAccountSheet}
                isUserLoggedIn={isUserLoggedIn}
                userDetails={userDetails}
                onUserLogout={() => { userLogout(); closeAccountSheet(); }}
                onUserLogin={() => { setIsLoginModalOpen(true); closeAccountSheet(); }}
                onAccountClick={() => { closeAccountSheet(); router.push('/account'); }}
            />
            <LoginModal
                isOpen={isLoginModalOpen}
                onClose={() => setIsLoginModalOpen(false)}
                dismissable={customerFacing || isUserLoggedIn}
            />

            {isCartDrawerOpen && (
                <CartDrawer
                    isOpen={isCartDrawerOpen}
                    onClose={() => setIsCartDrawerOpen(false)}
                    cartDetails={cartDetailsQuery.data}
                    isLoading={cartDetailsQuery.isLoading}
                    updateCartItemQuantity={updateCartItemQuantityMutation.mutate}
                    isUpdatingQuantity={updateCartItemQuantityMutation.isPending}
                    cartNotifications={cartNotifications}
                    onDismissNotification={dismissCartNotification}
                    onValidateCart={() => validateCartMutation.mutate()}
                    isValidatingCart={validateCartMutation.isPending}
                    isLoggedIn={hasActiveCustomerSession}
                    onLogin={() => {
                        setIsCartDrawerOpen(false);
                        if (!isUserLoggedIn) {
                            setIsLoginModalOpen(true);
                        } else {
                            console.log("CartDrawer: Proceeding to checkout (user logged in).");
                        }
                    }}
                    sessionData={{
                        customerName: activeSessionDetails?.customerName,
                        customerPhone: activeSessionDetails?.customerPhone,
                        location: activeSessionDetails?.location
                    }}
                    onCheckoutComplete={() => {
                        setIsCartDrawerOpen(false);
                    }}
                />
            )}
        </>
    );
} 
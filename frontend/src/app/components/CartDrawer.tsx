"use client";

import React, { useCallback, useState, useMemo, memo } from 'react';
import { CartDetails, DetailedCartItem, CartItem } from '../types/session';
import { useTranslation } from 'react-i18next';
import { useSession } from '../context/SessionContext';
import { useOrderSuccess } from '../context/OrderSuccessContext';
import CartItemCard from './CartDrawer/CartItemCard';
import SavingsBanner from './CartDrawer/SavingsBanner';
import DiscountBanner from './CartDrawer/DiscountBanner';
import BillDetails from './CartDrawer/BillDetails';
import ProceedToCheckoutButton from './CartDrawer/ProceedToCheckoutButton';
import CartNotifications from './CartDrawer/CartNotifications';
import CheckoutModal from './CartDrawer/CheckoutModal';
import { clarityUtils } from './common/MicrosoftClarity';
import { toast } from 'react-toastify';
import { t } from 'i18next';
import { logger } from '@/lib/logger';
import { isCustomerFacingMode } from '@/lib/utils';

interface CartDrawerProps {
    isOpen: boolean;
    onClose: () => void;
    isLoggedIn: boolean;
    onLogin: () => void;
    cartDetails: (CartDetails & { removedItems?: Array<{ item: CartItem; reason: string }> }) | undefined;
    updateCartItemQuantity: (args: { productId: number; variantId?: number; newQuantity: number }) => void;
    isLoading?: boolean;
    isUpdatingQuantity?: boolean;
    cartNotifications?: Array<{ id: string; message: string; type: 'info' | 'warning' | 'error' }>;
    onDismissNotification?: (id: string) => void;
    onValidateCart?: () => void;
    isValidatingCart?: boolean;
    // Session data for checkout
    sessionData?: {
        customerName?: string;
        customerPhone?: string;
        landmark?: string;
        location?: { lat: number; lng: number };
    };
    onCheckoutComplete?: () => void;
}

const DELIVERY_CHARGE = 0;
const HANDLING_CHARGE = 0;

// Custom hook for cart calculations
const useCartCalculations = (cartDetails: CartDetails | undefined) => {
    return useMemo(() => {
        const itemsTotalPrice = cartDetails?.itemsTotalPrice || 0;
        const discountInfo = cartDetails?.discountInfo;

        // Calculate final total considering discounts
        const finalTotal = discountInfo?.finalTotal || itemsTotalPrice;
        const grandTotal = finalTotal + DELIVERY_CHARGE + HANDLING_CHARGE;

        const hasItems = cartDetails ? cartDetails.detailedCartItems.length > 0 : false;
        const totalItems = cartDetails?.detailedCartItems.reduce((sum, item) => sum + item.quantity, 0) || 0;

        // Calculate savings breakdown
        const regularSavings = cartDetails?.totalSavings || 0; // MRP vs selling price savings
        const discountSavings = discountInfo?.totalDiscount || 0; // Cart discount savings
        const combinedSavings = regularSavings + discountSavings; // Total savings

        return {
            grandTotal,
            hasItems,
            totalItems,
            itemsTotalPrice,
            // Savings breakdown
            regularSavings,
            discountSavings,
            combinedSavings,
            totalSavings: combinedSavings, // For backward compatibility, use combined savings
            // Discount-related calculations
            discountInfo,
            finalTotal,
            hasDiscounts: discountInfo && discountInfo.totalDiscount > 0,
            totalDiscount: discountInfo?.totalDiscount || 0,
            appliedDiscounts: discountInfo?.appliedDiscounts || []
        };
    }, [cartDetails]);
};

// Custom hook for checkout logic
const useCheckoutFlow = (
    activeSessionId: string | null,
    sessions: any[],
    removeSession: (id: string) => void,
    checkoutMutation: any,
    showOrderSuccess: any,
    onClose: () => void,
    grandTotal: number,
    totalItems: number,
    onCheckoutComplete?: () => void
) => {
    const [isCheckoutModalOpen, setIsCheckoutModalOpen] = useState(false);

    const handleCheckout = useCallback(() => {
        setIsCheckoutModalOpen(true);
    }, []);

    const session = useSession();

    const handleCheckoutSubmit = useCallback(async (checkoutData: {
        customerName: string;
        customerPhone: string;
        landmark?: string;
        location: { lat: number; lng: number }
    }) => {
        try {
            const activeSession = sessions.find(s => s.id === activeSessionId);
            if (!activeSession || !activeSession.cartItems.length) {
                logger.error('CartDrawer: No active session or empty cart');
                throw new Error('No items in cart');
            }

            clarityUtils.trackCheckoutStart(grandTotal, totalItems);

            const checkoutRequest = {
                customerName: checkoutData.customerName,
                customerPhone: checkoutData.customerPhone,
                landmark: checkoutData.landmark,
                location: checkoutData.location,
                cartItems: activeSession.cartItems,
                cartId: activeSessionId
            };

            await checkoutMutation.mutateAsync(checkoutRequest);

            clarityUtils.trackOrderComplete(grandTotal, totalItems);

            setIsCheckoutModalOpen(false);
            showOrderSuccess({
                total: grandTotal,
                customerName: checkoutData.customerName
            });

            if (activeSessionId) {
                removeSession(activeSessionId);
            }

            onClose();
            onCheckoutComplete?.();
        } catch (error) {
            logger.error('CartDrawer: Checkout failed with error:', error);
            toast.error(t('cartDrawer.checkoutFailed', 'Checkout failed. Contact support if the issue persists.'));

            // Put back the customer details in the session data
            if (activeSessionId) {
                const activeSession = sessions.find(s => s.id === activeSessionId);
                if (activeSession) {
                    activeSession.customerName = checkoutData.customerName;
                    activeSession.customerPhone = checkoutData.customerPhone;
                    activeSession.landmark = checkoutData.landmark;
                }
                session.updateSession(activeSessionId, activeSession);
            }

        }
    }, [activeSessionId, sessions, removeSession, checkoutMutation, showOrderSuccess, onClose, onCheckoutComplete, grandTotal, totalItems, session]);

    const closeCheckoutModal = useCallback(() => {
        setIsCheckoutModalOpen(false);
    }, []);

    return {
        isCheckoutModalOpen,
        handleCheckout,
        handleCheckoutSubmit,
        closeCheckoutModal
    };
};

// Memoized Cart Header Component
const CartHeader = memo(function CartHeader({
    onClose,
    onValidateCart,
    isValidatingCart,
    hasItems
}: {
    onClose: () => void;
    onValidateCart?: () => void;
    isValidatingCart?: boolean;
    hasItems: boolean;
}) {
    const { t } = useTranslation();

    return (
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
            <div className="flex items-center space-x-3">
                <h2 className="text-lg font-semibold text-gray-800">{t('cart.myCart', 'My Cart')}</h2>
                {onValidateCart && hasItems && (
                    <button
                        onClick={onValidateCart}
                        disabled={isValidatingCart}
                        className="text-sm px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        title={t('cart.validateCart', 'Check for availability')}
                    >
                        {isValidatingCart ? (
                            <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        ) : (
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                        )}
                    </button>
                )}
            </div>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
    );
});

// Memoized Loading Overlay Component
const LoadingOverlay = memo(function LoadingOverlay({
    isLoading,
    isUpdatingQuantity,
    isValidatingCart
}: {
    isLoading?: boolean;
    isUpdatingQuantity?: boolean;
    isValidatingCart?: boolean;
}) {
    const { t } = useTranslation();

    if (!isLoading && !isUpdatingQuantity && !isValidatingCart) return null;

    return (
        <div className="absolute inset-0 bg-white/70 flex items-center justify-center z-10">
            <p>
                {isValidatingCart
                    ? t('cart.validating', 'Validating cart...')
                    : isUpdatingQuantity
                        ? t('cart.updating', 'Updating cart...')
                        : t('cart.loading', 'Loading cart...')
                }
            </p>
        </div>
    );
});

// Memoized Empty Cart Component
const EmptyCart = memo(function EmptyCart({ onClose }: { onClose: () => void }) {
    const { t } = useTranslation();

    return (
        <div className="text-center py-10">
            <p className="text-gray-500">{t('cart.emptyCart', 'Your cart is empty.')}</p>
            <button
                onClick={onClose}
                className="mt-4 px-6 py-2 bg-[var(--color-green-600)] text-white rounded-md hover:brightness-90 transition-all"
            >
                {t('cart.continueShopping', 'Continue Shopping')}
            </button>
        </div>
    );
});

// Memoized Cart Items List Component
const CartItemsList = memo(function CartItemsList({
    items,
    onQuantityChange,
    isUpdatingQuantity,
    isLoading
}: {
    items: DetailedCartItem[];
    onQuantityChange: (item: DetailedCartItem, newQuantity: number) => void;
    isUpdatingQuantity?: boolean;
    isLoading?: boolean;
}) {
    return (
        <div className="bg-white rounded-lg shadow-sm divide-y divide-gray-200">
            {items.map(item => (
                <CartItemCard
                    key={`${item.skuId}-${item.variantSkuId || 'base'}`}
                    item={item}
                    onQuantityChange={onQuantityChange}
                    isUpdatingQuantity={isUpdatingQuantity}
                    isLoading={isLoading}
                />
            ))}
        </div>
    );
});

const CartDrawer = memo(function CartDrawer({
    isOpen,
    onClose,
    isLoggedIn,
    onLogin,
    cartDetails,
    updateCartItemQuantity,
    isLoading,
    isUpdatingQuantity,
    cartNotifications = [],
    onDismissNotification,
    onValidateCart,
    isValidatingCart = false,
    sessionData,
    onCheckoutComplete
}: CartDrawerProps) {
    const { checkoutMutation, sessions, activeSessionId, removeSession, updateSession } = useSession();
    const { showOrderSuccess } = useOrderSuccess();

    // Custom hooks
    const calculations = useCartCalculations(cartDetails);
    const checkout = useCheckoutFlow(
        activeSessionId,
        sessions,
        removeSession,
        checkoutMutation,
        showOrderSuccess,
        onClose,
        calculations.grandTotal,
        calculations.totalItems,
        onCheckoutComplete
    );

    // Memoized quantity change handler
    const handleQuantityChange = useCallback((item: DetailedCartItem, newQuantity: number) => {
        if (newQuantity < 0) return;

        updateCartItemQuantity({
            productId: item.skuId,
            variantId: item.variantSkuId,
            newQuantity: newQuantity,
        });
    }, [updateCartItemQuantity]);

    // Memoized initial checkout data
    const initialCheckoutData = useMemo(() => ({
        customerName: sessionData?.customerName || '',
        customerPhone: sessionData?.customerPhone || '',
        landmark: sessionData?.landmark || '',
        location: sessionData?.location
    }), [sessionData]);

    // Handle customer data changes from CheckoutModal with auto-sync
    const handleCustomerDataChange = useCallback(async (customerData: Partial<{
        customerName: string;
        customerPhone: string;
        landmark?: string;
        location: { lat: number; lng: number };
    }>) => {
        if (!activeSessionId) return;

        try {
            // Update session data immediately
            const activeSession = sessions.find(s => s.id === activeSessionId);
            if (activeSession) {
                const updatedSession = {
                    ...activeSession,
                    customerName: customerData.customerName || activeSession.customerName,
                    customerPhone: customerData.customerPhone || activeSession.customerPhone,
                    landmark: customerData.landmark !== undefined ? customerData.landmark : activeSession.landmark,
                    location: customerData.location || activeSession.location
                };
                updateSession(activeSessionId, updatedSession);

                // Import syncCartToBackend dynamically to avoid circular dependency
                const { syncCartToBackend } = await import('../services/cartService');

                // Sync to backend (fire-and-forget)
                syncCartToBackend(activeSessionId, activeSession.cartItems, {
                    customerName: updatedSession.customerName,
                    customerPhone: updatedSession.customerPhone,
                    landmark: updatedSession.landmark,
                    location: updatedSession.location
                });

                logger.info('CartDrawer: Customer data synced', {
                    sessionId: activeSessionId,
                    hasName: !!updatedSession.customerName,
                    hasPhone: !!updatedSession.customerPhone,
                    hasLocation: !!updatedSession.location
                });
            }
        } catch (error) {
            logger.error('CartDrawer: Failed to sync customer data', error);
            // Don't throw error - this is auto-sync, should be non-blocking
        }
    }, [activeSessionId, sessions, updateSession]);

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex justify-end">
            {/* Overlay */}
            <div className="fixed inset-0 bg-black/50 transition-opacity duration-300 ease-in-out" onClick={onClose}></div>

            {/* Drawer Panel */}
            <div className="relative w-full max-w-md bg-gray-50 shadow-xl flex flex-col h-full transition-transform duration-300 ease-in-out transform translate-x-0">

                {/* Header */}
                <CartHeader
                    onClose={onClose}
                    onValidateCart={onValidateCart}
                    isValidatingCart={isValidatingCart}
                    hasItems={calculations.hasItems}
                />

                {/* Content - Scrollable */}
                <div className="flex-grow overflow-y-auto p-4 space-y-6 bg-gray-50">
                    <LoadingOverlay
                        isLoading={isLoading}
                        isUpdatingQuantity={isUpdatingQuantity}
                        isValidatingCart={isValidatingCart}
                    />

                    {!calculations.hasItems ? (
                        <EmptyCart onClose={onClose} />
                    ) : (
                        <>
                            {/* Discount Banner */}
                            {calculations.hasDiscounts && (
                                <DiscountBanner
                                    appliedDiscounts={calculations.appliedDiscounts}
                                    totalDiscount={calculations.totalDiscount}
                                />
                            )}

                            {/* Cart Items List */}
                            <CartItemsList
                                items={cartDetails!.detailedCartItems}
                                onQuantityChange={handleQuantityChange}
                                isUpdatingQuantity={isUpdatingQuantity}
                                isLoading={isLoading}
                            />

                            {/* Bill Details */}
                            <BillDetails
                                itemsTotalPrice={calculations.itemsTotalPrice}
                                itemsTotalMRP={cartDetails!.itemsTotalMRP}
                                itemSavings={calculations.regularSavings}
                                totalSavings={calculations.totalSavings}
                                deliveryCharge={DELIVERY_CHARGE}
                                handlingCharge={HANDLING_CHARGE}
                                grandTotal={calculations.grandTotal}
                                discountInfo={calculations.discountInfo}
                                finalTotal={calculations.finalTotal}
                                regularSavings={calculations.regularSavings}
                                discountSavings={calculations.discountSavings}
                                combinedSavings={calculations.combinedSavings}
                            />
                        </>
                    )}
                </div>

                {/* Footer - Action Button */}
                {calculations.hasItems && (
                    <ProceedToCheckoutButton
                        grandTotal={calculations.grandTotal}
                        isUserLoggedIn={isLoggedIn}
                        isLoading={isLoading}
                        onLogin={onLogin}
                        onCloseCart={onClose}
                        onCheckout={checkout.handleCheckout}
                    />
                )}

                {/* Checkout Modal */}
                <CheckoutModal
                    isOpen={checkout.isCheckoutModalOpen}
                    onClose={checkout.closeCheckoutModal}
                    onCheckout={checkout.handleCheckoutSubmit}
                    initialData={initialCheckoutData}
                    grandTotal={calculations.grandTotal}
                    isProcessing={checkoutMutation.isPending}
                    isCustomerFacingMode={isCustomerFacingMode()}
                    onCustomerDataChange={handleCustomerDataChange}
                />
            </div>
        </div>
    );
});

export default CartDrawer;
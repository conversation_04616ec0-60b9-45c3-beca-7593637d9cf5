import React from 'react';
import { useTranslation } from 'react-i18next';
import { AppliedDiscount, CartDiscountInfo } from '@/app/types/session';

interface BillDetailsProps {
    itemsTotalPrice: number;
    itemsTotalMRP: number;
    itemSavings: number; // MRP vs selling price savings
    totalSavings: number; // Combined savings (for backward compatibility)
    deliveryCharge: number;
    handlingCharge: number;
    grandTotal: number;
    // Discount-related props
    discountInfo?: CartDiscountInfo;
    finalTotal: number;
    // Savings breakdown
    regularSavings: number; // MRP vs selling price savings
    discountSavings: number; // Cart discount savings
    combinedSavings: number; // Total savings
}

const BillDetails: React.FC<BillDetailsProps> = React.memo(({
    itemsTotalPrice,
    itemsTotalMRP,
    itemSavings,
    totalSavings,
    grandTotal,
    discountInfo,
    finalTotal,
    regularSavings,
    discountSavings,
    combinedSavings,
}) => {
    const { t } = useTranslation();

    return (
        <div className="bg-white p-4 rounded-lg shadow-sm space-y-2 text-sm">
            <h3 className="text-md font-semibold text-gray-800 mb-3">{t('cartDrawer.billDetails', 'Bill details')}</h3>
            <div className="flex justify-between">
                <span className="text-gray-600 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" /></svg>
                    {t('cartDrawer.itemsTotal', 'Items total')}
                    {itemSavings > 0 &&
                        <span className="ml-2 bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded-full text-xs font-medium">{t('cartDrawer.saved', 'Saved')} ₹{itemSavings}</span>}
                </span>
                <div className="text-right">
                    {itemsTotalMRP > itemsTotalPrice && <span className="text-gray-400 line-through mr-1.5">₹{itemsTotalMRP}</span>}
                    <span className="text-gray-800 font-medium">₹{itemsTotalPrice}</span>
                </div>
            </div>

            {/* Clean Discount Section */}
            {discountInfo && discountInfo.appliedDiscounts.length > 0 && (
                <div className="space-y-2">
                    {discountInfo.appliedDiscounts.map((discount, index) => (
                        <div key={discount.discountId} className="flex justify-between">
                            <span className="text-gray-600 flex items-center text-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                                </svg>
                                {discount.discountName}
                            </span>
                            <span className="text-green-600 font-medium text-sm">-₹{discount.discountAmount}</span>
                        </div>
                    ))}

                    {/* Subtotal after discounts */}
                    <div className="flex justify-between pt-2 border-t border-gray-100">
                        <span className="text-gray-600 text-sm">{t('cartDrawer.subtotalAfterDiscounts', 'Subtotal after discounts')}</span>
                        <span className="text-gray-800 font-medium">₹{finalTotal}</span>
                    </div>
                </div>
            )}

            {/* Show discount calculation error if any */}
            {discountInfo?.calculationError && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2 text-xs text-yellow-700">
                    <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                            <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        {t('cartDrawer.discountCalculationError', 'Discount calculation temporarily unavailable')}
                    </div>
                </div>
            )}

            {/* <div className="flex justify-between">
                <span className="text-gray-600 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-gray-400" viewBox="0 0 20 20" fill="currentColor"><path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" /><path d="M3 4a1 1 0 00-1 1v1h16V5a1 1 0 00-1-1H3zM19 8H1V7h18v1zM1 13a1 1 0 011-1h14a1 1 0 110 2H2a1 1 0 01-1-1zm17 0a1 1 0 100-2h-3a1 1 0 100 2h3zM4.043 8.022l1.531-.766A1.5 1.5 0 017 8.5V11a1 1 0 01-1.447.894L3.5 10.522a1 1 0 010-1.788l.043-.022zM16.043 8.022l-1.531-.766A1.5 1.5 0 0013 8.5V11a1 1 0 001.447.894L16.5 10.522a1 1 0 000-1.788l-.043-.022z" /></svg>
                    {t('cartDrawer.deliveryCharge', 'Delivery charge')}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 ml-1 text-gray-400 cursor-pointer" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" /></svg>
                </span>
                <span className={`${deliveryCharge === 0 ? 'text-green-600 font-medium' : 'text-gray-800 font-medium'}`}>
                    {deliveryCharge === 0 ? t('cartDrawer.free', 'FREE') : `₹${deliveryCharge}`}
                </span>
            </div> */}
            {/* <div className="flex justify-between">
                <span className="text-gray-600 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-gray-400" viewBox="0 0 20 20" fill="currentColor"><path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z" /><path fillRule="evenodd" d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clipRule="evenodd" /></svg>
                    {t('cartDrawer.handlingCharge', 'Handling charge')}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 ml-1 text-gray-400 cursor-pointer" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" /></svg>
                </span>
                <span className="text-gray-800 font-medium">₹{handlingCharge}</span>
            </div> */}
            <div className="border-t border-gray-200 pt-3 mt-3 flex justify-between">
                <span className="text-gray-800 font-semibold text-md">{t('cartDrawer.grandTotal', 'Grand total')}</span>
                <span className="text-gray-800 font-bold text-md">₹{grandTotal}</span>
            </div>
            {combinedSavings > 0 && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 mt-3">
                    <div className="flex justify-between items-center">
                        <div>
                            <span className="text-gray-700 font-medium text-sm">{t('cartDrawer.totalSavings', 'Your total savings')}</span>
                            {(regularSavings > 0 && discountSavings > 0) && (
                                <div className="text-xs text-gray-500 mt-0.5">
                                    {regularSavings > 0 && <span>Product savings: ₹{regularSavings}</span>}
                                    {(regularSavings > 0 && discountSavings > 0) && <span className="mx-1">•</span>}
                                    {discountSavings > 0 && <span>Discount savings: ₹{discountSavings}</span>}
                                </div>
                            )}
                        </div>
                        <span className="text-gray-800 font-semibold">₹{combinedSavings}</span>
                    </div>
                </div>
            )}
        </div>
    );
});

BillDetails.displayName = 'BillDetails';
export default BillDetails; 
import React from 'react';
import { useTranslation } from 'react-i18next';
// Import server-side discount types
import type {
    AppliedDiscount
} from '@/server/features/discount/discount.validation';

interface DiscountBannerProps {
    appliedDiscounts: AppliedDiscount[];
    totalDiscount: number;
}

const DiscountBanner: React.FC<DiscountBannerProps> = React.memo(({ 
    appliedDiscounts, 
    totalDiscount 
}) => {
    const { t } = useTranslation();

    if (!appliedDiscounts.length || totalDiscount <= 0) {
        return null;
    }

    return (
        <div className="bg-green-50 border border-green-100 rounded-md p-3">
            {/* Header */}
            <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                    <div className="bg-green-50 rounded-full p-1 mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                            <path strokeLinecap="round" strokeLinejoin="round" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                    </div>
                    <div>
                        <h3 className="text-green-700 font-medium text-sm">
                            {appliedDiscounts.length === 1
                                ? t('cartDrawer.discountApplied', 'Discount Applied')
                                : t('cartDrawer.discountsApplied', 'Discounts Applied')
                            }
                        </h3>
                        <p className="text-green-600 text-xs">
                            {appliedDiscounts.length === 1
                                ? t('cartDrawer.discountSavingMessage', 'You\'re saving money on this order')
                                : t('cartDrawer.discountsSavingMessage', 'Multiple discounts are saving you money')
                            }
                        </p>
                    </div>
                </div>
                <div className="bg-green-100 text-green-700 px-3 py-1 rounded-md">
                    <div className="text-center">
                        <div className="text-xs font-medium">Save</div>
                        <div className="text-sm font-semibold">₹{totalDiscount}</div>
                    </div>
                </div>
            </div>

            {/* Individual Discounts */}
            {appliedDiscounts.length > 1 && (
                <div className="space-y-1">
                    {appliedDiscounts.map((discount) => (
                        <div key={discount.discountId} className="flex justify-between items-center bg-white bg-opacity-50 rounded px-2 py-1">
                            <div className="flex items-center">
                                <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-2"></div>
                                <span className="text-green-600 font-normal text-xs">{discount.discountName}</span>
                            </div>
                            <span className="text-green-600 font-medium text-xs">-₹{discount.discountAmount}</span>
                        </div>
                    ))}
                </div>
            )}

            {/* Single discount details */}
            {appliedDiscounts.length === 1 && (
                <div className="bg-white bg-opacity-50 rounded px-2 py-1">
                    <div className="flex items-center justify-between">
                        <span className="text-green-600 font-normal text-xs">{appliedDiscounts[0].discountName}</span>
                        <span className="text-green-600 font-medium text-xs">-₹{appliedDiscounts[0].discountAmount}</span>
                    </div>
                </div>
            )}
        </div>
    );
});

DiscountBanner.displayName = 'DiscountBanner';
export default DiscountBanner;

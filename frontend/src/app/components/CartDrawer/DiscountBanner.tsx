import React from 'react';
import { useTranslation } from 'react-i18next';
import { AppliedDiscount } from '@/app/types/session';

interface DiscountBannerProps {
    appliedDiscounts: AppliedDiscount[];
    totalDiscount: number;
}

const DiscountBanner: React.FC<DiscountBannerProps> = React.memo(({ 
    appliedDiscounts, 
    totalDiscount 
}) => {
    const { t } = useTranslation();

    if (!appliedDiscounts.length || totalDiscount <= 0) {
        return null;
    }

    return (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 shadow-sm">
            {/* Header */}
            <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                    <div className="bg-green-100 rounded-full p-2 mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                            <path strokeLinecap="round" strokeLinejoin="round" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                    </div>
                    <div>
                        <h3 className="text-green-800 font-bold text-lg">
                            {appliedDiscounts.length === 1 
                                ? t('cartDrawer.discountApplied', 'Discount Applied!')
                                : t('cartDrawer.discountsApplied', 'Discounts Applied!')
                            }
                        </h3>
                        <p className="text-green-700 text-sm">
                            {appliedDiscounts.length === 1 
                                ? t('cartDrawer.discountSavingMessage', 'You\'re saving money on this order!')
                                : t('cartDrawer.discountsSavingMessage', 'Multiple discounts are saving you money!')
                            }
                        </p>
                    </div>
                </div>
                <div className="bg-green-600 text-white px-4 py-2 rounded-full shadow-md">
                    <div className="text-center">
                        <div className="text-xs font-medium opacity-90">SAVE</div>
                        <div className="text-xl font-bold">₹{totalDiscount}</div>
                    </div>
                </div>
            </div>

            {/* Individual Discounts */}
            {appliedDiscounts.length > 1 && (
                <div className="space-y-2">
                    {appliedDiscounts.map((discount) => (
                        <div key={discount.discountId} className="flex justify-between items-center bg-white bg-opacity-70 rounded-md px-3 py-2">
                            <div className="flex items-center">
                                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                <span className="text-green-800 font-medium text-sm">{discount.discountName}</span>
                            </div>
                            <span className="text-green-700 font-semibold text-sm">-₹{discount.discountAmount}</span>
                        </div>
                    ))}
                </div>
            )}

            {/* Single discount details */}
            {appliedDiscounts.length === 1 && (
                <div className="bg-white bg-opacity-70 rounded-md px-3 py-2">
                    <div className="flex items-center justify-between">
                        <span className="text-green-800 font-medium">{appliedDiscounts[0].discountName}</span>
                        <span className="text-green-700 font-semibold">-₹{appliedDiscounts[0].discountAmount}</span>
                    </div>
                </div>
            )}
        </div>
    );
});

DiscountBanner.displayName = 'DiscountBanner';
export default DiscountBanner;

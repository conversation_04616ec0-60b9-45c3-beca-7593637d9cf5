import { CartItem, CartDetails, DetailedCartItem, AdminCart, AdminCartFilters, GetCartsForAdminRequest, GetCartsForAdminResponse, ADMIN_CART_STATUSES, AdminCartStatus, CartDiscountInfo } from '../types/session';
import { SKU, SKUVariant } from '../types/sku';
import { getSkuById } from './skuService';
import axiosInstance from '../../lib/axios';
import { isCustomerFacingMode } from '../../lib/utils';
import { logger } from '@/lib/logger';
import { v4 as uuidv4 } from 'uuid';
import { discountHelpers } from '@/lib/trpc-client';


// Internal checkout data interface (used by UI components)
export interface InternalCheckoutData {
    customerName: string;
    customerPhone: string;
    landmark?: string;
    location: {
        lat: number;
        lng: number;
    };
    cartItems: CartItem[];
    cartId: string;
}

// API checkout request interface
export interface CheckoutRequest {
    cartJson: {
        skuItems: Array<{
            skuId: number;
            quantity: number;
            sellingPrice: number;
            costPrice: number;
            mrp: number;
            skuName: string;
            skuImage: string;
        }>;
        customer: {
            name: string;
            mobile: string;
            landmark?: string;
            location: {
                lat: string;
                lng: string;
            };
            address: string;
        };
    };
    cartId: string;
    userType: 'BHUMI' | 'BHUVANESH' | 'ADMIN';
    status: 'CREATED' | 'ORDERED' | 'CANCELLED' | 'UNREACHABLE_1' | 'UNREACHABLE_2';
}

export interface CheckoutResponse {
    success: boolean;
    message: string;
}

// Helper to find product details (simulates a backend lookup or utility)
const getProductDetails = async (skuId: number, variantSkuId?: number): Promise<{ sku: SKU, selectedVariant?: SKUVariant } | undefined> => {
    const sku = await getSkuById(skuId);
    if (sku) {
        if (variantSkuId && sku.variants) {
            const selectedVariant = sku.variants.find(v => v.skuId === variantSkuId);
            return { sku: { ...sku }, selectedVariant: selectedVariant ? { ...selectedVariant } : undefined };
        }
        return { sku: { ...sku } };
    }
    return undefined;
};

/**
 * Validates if a cart item's SKU and variant are still available
 */
export const validateCartItem = async (item: CartItem): Promise<{ isValid: boolean; reason?: string }> => {
    try {
        const details = await getProductDetails(item.skuId, item.variantSkuId);

        if (!details) {
            return {
                isValid: false,
                reason: 'Product no longer available'
            };
        }

        const { sku, selectedVariant } = details;

        // Check if variant exists when specified
        if (item.variantSkuId && !selectedVariant) {
            return {
                isValid: false,
                reason: 'Product variant no longer available'
            };
        }

        // Additional availability checks can be added here
        // For example: stock levels, active status, etc.

        return { isValid: true };
    } catch (error) {
        logger.error('cartService: Error validating cart item:', error);
        return {
            isValid: false,
            reason: 'Unable to verify product availability'
        };
    }
};

/**
 * Validates and cleans up cart items, removing invalid ones
 */
export const validateAndCleanupCart = async (cartItems: CartItem[]): Promise<{
    validItems: CartItem[];
    removedItems: Array<{ item: CartItem; reason: string }>;
}> => {
    const validItems: CartItem[] = [];
    const removedItems: Array<{ item: CartItem; reason: string }> = [];

    for (const item of cartItems) {
        const validation = await validateCartItem(item);

        if (validation.isValid) {
            validItems.push(item);
        } else {
            removedItems.push({
                item: { ...item },
                reason: validation.reason || 'Unknown reason'
            });
        }
    }

    return { validItems, removedItems };
};

/**
 * Enhanced getCartDetails with validation and cleanup
 */
export const getCartDetails = async (cartItems: CartItem[]): Promise<CartDetails & {
    removedItems?: Array<{ item: CartItem; reason: string }>
}> => {
    const detailedCartItems: DetailedCartItem[] = [];
    let itemsTotalPrice = 0;
    let itemsTotalMRP = 0;
    let totalCartQuantity = 0;
    const removedItems: Array<{ item: CartItem; reason: string }> = [];

    for (const item of cartItems) {
        const details = await getProductDetails(item.skuId, item.variantSkuId);

        if (details) {
            const { sku: product, selectedVariant } = details;

            // Check if variant exists when specified
            if (item.variantSkuId && !selectedVariant) {
                removedItems.push({
                    item: { ...item },
                    reason: 'Product variant no longer available'
                });
                continue;
            }

            const pricePerUnit = selectedVariant ? (selectedVariant.sellingPrice || 0) : (product.sellingPrice || 0);
            const mrpPerUnit = selectedVariant ? (selectedVariant.mrp || 0) : (product.mrp || 0);

            detailedCartItems.push({
                skuId: product.skuId,
                variantSkuId: selectedVariant?.skuId,
                name: product.name,
                variantName: selectedVariant?.name,
                imageUrl: product.imageUrl,
                quantity: item.quantity,
                pricePerUnit,
                mrpPerUnit,
            });
            itemsTotalPrice += pricePerUnit * item.quantity;
            itemsTotalMRP += mrpPerUnit * item.quantity;
            totalCartQuantity += item.quantity;
        } else {
            // SKU not found - add to removed items
            removedItems.push({
                item: { ...item },
                reason: 'Product no longer available'
            });
        }
    }

    const result: CartDetails & { removedItems?: Array<{ item: CartItem; reason: string }> } = {
        detailedCartItems,
        itemsTotalPrice,
        itemsTotalMRP,
        totalSavings: itemsTotalMRP - itemsTotalPrice,
        totalCartQuantity,
    };

    // Calculate discounts if cart has items
    if (detailedCartItems.length > 0) {
        try {
            // Prepare cart items for discount calculation
            const cartItemsForDiscount = detailedCartItems.map(item => ({
                skuId: item.skuId,
                variantSkuId: item.variantSkuId,
                quantity: item.quantity,
                pricePerUnit: item.pricePerUnit,
                mrpPerUnit: item.mrpPerUnit,
            }));

            // Calculate discounts using tRPC helper
            const discountResult = await discountHelpers.calculateDiscounts(cartItemsForDiscount);

            // Add discount information to result
            result.discountInfo = {
                totalDiscount: discountResult.totalDiscount,
                appliedDiscounts: discountResult.appliedDiscounts.map(discount => ({
                    discountId: discount.discountId,
                    discountName: discount.discountName,
                    discountAmount: discount.discountAmount,
                    discountType: discount.discountType,
                })),
                originalTotal: discountResult.originalTotal,
                finalTotal: discountResult.finalTotal,
                discountSavings: discountResult.savings,
            };

            logger.info('cartService: Discount calculation successful', {
                totalDiscount: discountResult.totalDiscount,
                appliedDiscounts: discountResult.appliedDiscounts.length,
                finalTotal: discountResult.finalTotal
            });

        } catch (error) {
            // Graceful degradation - log error but don't fail cart calculation
            logger.warn('cartService: Discount calculation failed, proceeding without discounts', { error });

            // Add error info to result for debugging
            result.discountInfo = {
                totalDiscount: 0,
                appliedDiscounts: [],
                originalTotal: itemsTotalPrice,
                finalTotal: itemsTotalPrice,
                discountSavings: 0,
                calculationError: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    // Include removed items info if any were removed
    if (removedItems.length > 0) {
        result.removedItems = removedItems;
    }

    return result;
};

/**
 * Simulates adding an item to the cart or updating its quantity.
 */
export const addItemToCart = async (currentCart: CartItem[], newItem: CartItem): Promise<CartItem[]> => {
    const updatedCart = [...currentCart];
    const existingItemIndex = updatedCart.findIndex(
        cartItem => cartItem.skuId === newItem.skuId && cartItem.variantSkuId === newItem.variantSkuId
    );

    if (existingItemIndex >= 0) {
        updatedCart[existingItemIndex] = {
            ...updatedCart[existingItemIndex],
            quantity: updatedCart[existingItemIndex].quantity + newItem.quantity,
        };
        // Ensure quantity doesn't drop below 0, remove if it does
        if (updatedCart[existingItemIndex].quantity <= 0) {
            updatedCart.splice(existingItemIndex, 1);
        }
    } else if (newItem.quantity > 0) {
        updatedCart.push({ ...newItem });
    }

    const result = updatedCart.map(item => ({ ...item }));
    logger.info('cartService: addItemToCart returning:', { itemCount: result.length });
    return Promise.resolve(result);
};

/**
 * Simulates removing an item from the cart.
 */
export const removeItemFromCart = async (currentCart: CartItem[], skuId: number, variantSkuId?: number): Promise<CartItem[]> => {
    const updatedCart = currentCart.filter(
        item => !(item.skuId === skuId && (variantSkuId === undefined || item.variantSkuId === variantSkuId))
    );
    return Promise.resolve(updatedCart.map(item => ({ ...item })));
};

/**
 * Simulates updating an item's quantity in the cart.
 * If newQuantity is 0, the item is removed.
 */
export const updateCartItemQuantity = async (
    currentCart: CartItem[],
    skuId: number,
    variantSkuId: number | undefined,
    newQuantity: number
): Promise<CartItem[]> => {
    const updatedCart = [...currentCart];
    const itemIndex = updatedCart.findIndex(
        item => item.skuId === skuId && item.variantSkuId === variantSkuId
    );

    if (itemIndex >= 0) {
        if (newQuantity > 0) {
            updatedCart[itemIndex] = { ...updatedCart[itemIndex], quantity: newQuantity };
        } else {
            updatedCart.splice(itemIndex, 1); // Remove item if quantity is 0 or less
        }
    } else if (newQuantity > 0) {
        // This case should ideally not happen if item is only updated.
        // If it can, means we are adding a new item here.
        // To be safe, let's ensure newItem has id, variantId, and quantity
        updatedCart.push({ skuId: skuId, variantSkuId: variantSkuId, quantity: newQuantity });
    }
    return Promise.resolve(updatedCart.map(item => ({ ...item })));
};

const getCheckoutEndpoint = () => {
    return isCustomerFacingMode() ? '/userApp-infinity-upsertCartByBhumi' : '/userApp-infinity-upsertCart';
}

/**
 * Sync cart to backend - fire-and-forget approach
 */
export const syncCartToBackend = async (
    sessionId: string,
    cartItems: CartItem[],
    customerData?: {
        customerName?: string;
        customerPhone?: string;
        landmark?: string;
        location?: { lat: number; lng: number };
    }
): Promise<void> => {
    try {
        // Transform cart items to include pricing details (same as checkout)
        const skuItems = [];

        for (const cartItem of cartItems) {
            const productDetails = await getProductDetails(cartItem.skuId, cartItem.variantSkuId);

            if (!productDetails) {
                logger.warn('syncCartToBackend: Product details not found, skipping item', { skuId: cartItem.skuId });
                continue; // Skip invalid items instead of throwing error
            }

            const { sku, selectedVariant } = productDetails;

            // Use variant pricing if available, otherwise use base SKU pricing
            const sellingPrice = selectedVariant ? (selectedVariant.sellingPrice || 0) : (sku.sellingPrice || 0);
            const costPrice = selectedVariant ? (selectedVariant.costPrice || 0) : (sku.costPrice || 0);
            const mrp = selectedVariant ? (selectedVariant.mrp || 0) : (sku.mrp || 0);

            // Use variantSkuId if available, otherwise use base skuId
            const skuId = selectedVariant ? selectedVariant.skuId : sku.skuId;

            skuItems.push({
                skuId,
                quantity: cartItem.quantity,
                sellingPrice,
                costPrice,
                mrp,
                skuName: sku.name.en,
                skuImage: sku.imageUrl
            });
        }

        // Determine user type based on app mode
        const userType = isCustomerFacingMode() ? 'BHUMI' : 'BHUVANESH';

        // Build the API request for cart sync
        const syncRequest: CheckoutRequest = {
            cartJson: {
                skuItems, // Can be empty array for empty cart
                customer: {
                    name: customerData?.customerName || 'Guest',
                    mobile: customerData?.customerPhone || '',
                    landmark: customerData?.landmark,
                    location: {
                        lat: customerData?.location?.lat?.toString() || '0',
                        lng: customerData?.location?.lng?.toString() || '0'
                    },
                    address: '' // Empty for now as per requirements
                }
            },
            cartId: sessionId, // Use session ID as cart ID
            userType,
            status: 'CREATED' // Cart sync uses CREATED status
        };

        // Fire-and-forget API call - don't wait for response
        logger.info('syncCartToBackend: About to make API call', {
            sessionId,
            itemCount: skuItems.length,
            userType,
            hasCustomerData: !!(customerData?.customerName || customerData?.customerPhone)
        });
        axiosInstance.post(getCheckoutEndpoint(), syncRequest)
            .then(response => {
                logger.info('syncCartToBackend: API call successful', { sessionId, statusCode: response.status });
            })
            .catch(error => {
                logger.error('syncCartToBackend: Failed to sync cart to backend', {
                    sessionId,
                    error: error.message,
                    statusCode: error.response?.status,
                    errorData: error.response?.data
                });
                // Don't throw error - this is fire-and-forget
            });

        console.log(`syncCartToBackend: Cart sync initiated for session ${sessionId} with ${skuItems.length} items`);

    } catch (error) {
        console.error('syncCartToBackend: Error preparing cart sync:', error);
        // Don't throw error - this is fire-and-forget
    }
};

/**
 * Process checkout - sends order to backend
 */
export const processCheckout = async (internalData: InternalCheckoutData, shouldRetryWithNewCartId: boolean = true): Promise<CheckoutResponse> => {
    try {
        // Transform cart items to include pricing details
        const skuItems = [];

        for (const cartItem of internalData.cartItems) {
            const productDetails = await getProductDetails(cartItem.skuId, cartItem.variantSkuId);

            if (!productDetails) {
                throw new Error(`Product details not found for SKU ${cartItem.skuId}`);
            }

            const { sku, selectedVariant } = productDetails;

            // Use variant pricing if available, otherwise use base SKU pricing
            const sellingPrice = selectedVariant ? (selectedVariant.sellingPrice || 0) : (sku.sellingPrice || 0);
            const costPrice = selectedVariant ? (selectedVariant.costPrice || 0) : (sku.costPrice || 0);
            const mrp = selectedVariant ? (selectedVariant.mrp || 0) : (sku.mrp || 0);

            // Use variantSkuId if available, otherwise use base skuId
            const skuId = selectedVariant ? selectedVariant.skuId : sku.skuId;

            skuItems.push({
                skuId,
                quantity: cartItem.quantity,
                sellingPrice,
                costPrice,
                mrp,
                skuName: sku.name.en,
                skuImage: sku.imageUrl
            });
        }

        // Determine user type based on app mode
        const userType = isCustomerFacingMode() ? 'BHUMI' : 'BHUVANESH';

        // Build the API request
        const checkoutRequest: CheckoutRequest = {
            cartJson: {
                skuItems,
                customer: {
                    name: internalData.customerName,
                    mobile: internalData.customerPhone,
                    landmark: internalData.landmark,
                    location: {
                        lat: internalData.location.lat.toString(),
                        lng: internalData.location.lng.toString()
                    },
                    address: '' // Empty for now as per requirements
                }
            },
            cartId: internalData.cartId,
            userType,
            status: 'ORDERED'
        };

        // Make API call
        const responseData = await axiosInstance.post(getCheckoutEndpoint(), checkoutRequest);

        // The axios interceptor handles the response format and returns the data directly
        // Since we don't know the exact structure, we'll handle it safely
        if (responseData && typeof responseData === 'object') {
            return {
                success: true,
                message: (responseData as any).message || 'Order placed successfully'
            };
        } else {
            // Fallback if response is not an object
            return {
                success: true,
                message: 'Order placed successfully'
            };
        }

    } catch (error) {
        const errorCode = (error as any)?.code;
        if (errorCode == 'ERR_CANNOT_UPDATE_CART' && shouldRetryWithNewCartId) {
            const newInternalData = {
                ...internalData,
                cartId: uuidv4()
            };

            return processCheckout(newInternalData, false);
        }


        logger.error('cartService: Checkout error:', error);
        throw new Error('Failed to process checkout. Please try again.');
    }
};

// ==================== ADMIN CART FUNCTIONS ====================

/**
 * Normalizes an admin cart object to handle null/undefined values
 * @param cart Raw cart object from API
 * @returns Normalized cart object with safe defaults
 */
const normalizeAdminCart = (cart: Record<string, unknown>): AdminCart => {
    try {
        console.log('[normalizeAdminCart] Input cart:', cart);

        // Validate and normalize status
        const rawStatus = cart.status as string;
        const status = ADMIN_CART_STATUSES.includes(rawStatus as AdminCartStatus) ? rawStatus as AdminCartStatus : 'CREATED';

        // Ensure cartJson exists and has proper structure
        const cartJson = cart.cartJson as any || {};
        const customer = cartJson.customer || {};
        const skuItems = Array.isArray(cartJson.skuItems) ? cartJson.skuItems : [];

        // Handle different timestamp field names that might come from API
        const createdTimestamp = (cart.rowCreatedAt as number) || (cart.createdAt as number) || Date.now();
        const updatedTimestamp = (cart.rowUpdatedAt as number) || (cart.updatedAt as number) || Date.now();

        const normalizedCart: AdminCart = {
            id: (cart.id as number) || 0,
            assisted_user_id: cart.assisted_user_id as number | null,
            status,
            is_processed: (cart.is_processed as number) || 0,
            customerCode: (cart.customerCode as string) || '',
            userId: (cart.userId as number) || 0,
            cartId: (cart.cartId as string) || '',
            cartJson: {
                customer: {
                    name: customer.name || '',
                    mobile: customer.mobile || '',
                    address: customer.address || '',
                    location: {
                        lat: customer.location?.lat || '0',
                        lng: customer.location?.lng || '0'
                    }
                },
                skuItems: skuItems.map((item: any) => ({
                    mrp: item.mrp || 0,
                    skuId: item.skuId || 0,
                    quantity: item.quantity || 0,
                    costPrice: item.costPrice || 0,
                    sellingPrice: item.sellingPrice || item.price || 0,
                    skuName: item.skuName || '',
                    skuImage: item.skuImage || ''
                }))
            },
            rowCreatedAt: createdTimestamp,
            createdBy: (cart.createdBy as number) || 0,
            rowUpdatedAt: updatedTimestamp,
            updatedBy: (cart.updatedBy as number) || 0
        };

        console.log('[normalizeAdminCart] Output normalized cart:', normalizedCart);
        return normalizedCart;
    } catch (error) {
        console.error('[normalizeAdminCart] Error normalizing cart:', error);
        console.error('[normalizeAdminCart] Input was:', cart);
        throw error;
    }
};

/**
 * Converts date string to epoch seconds
 * @param dateString Date string in YYYY-MM-DD format
 * @returns Epoch seconds (Unix timestamp)
 */
const dateToEpochSeconds = (dateString: string, isEndOfDay: boolean = false): number => {
    logger.debug('Cart Service: Converting date string to epoch seconds', { dateString, isEndOfDay });
    const date = new Date(dateString + (isEndOfDay ? 'T23:59:59.999' : 'T00:00:00.000'));
    return Math.floor(date.getTime() / 1000);
};

/**
 * Fetches carts for admin with pagination and filtering
 * @param pageNumber Page number (1-based)
 * @param limit Number of items per page
 * @param filters Optional server-side filters
 * @returns Promise that resolves with carts and pagination info
 */
export const getCartsForAdmin = async (
    pageNumber: number = 1,
    limit: number = 20,
    filters?: {
        status?: string;
        dateFrom?: string; // YYYY-MM-DD format
        dateTo?: string; // YYYY-MM-DD format
    }
): Promise<{
    carts: AdminCart[];
    pagination: {
        currentPage: number;
        totalPages: number;
        totalRows: number;
        appliedLimit: number;
    };
}> => {
    try {
        logger.debug('Cart Service: Fetching admin carts', { pageNumber, limit, filters });

        const requestData: GetCartsForAdminRequest = {
            limit: limit.toString(), // API expects string
            pageNumber
        };

        // Add server-side filters if provided
        if (filters) {
            // Add status filter (omit if empty/all statuses)
            if (filters.status && filters.status.trim() !== '') {
                requestData.status = filters.status;
            }

            // Add date filters (convert to epoch seconds)
            if (filters.dateFrom && filters.dateTo) {
                requestData.fromDate = dateToEpochSeconds(filters.dateFrom) * 1000;
                requestData.toDate = dateToEpochSeconds(filters.dateTo, true) * 1000; // End of day
            }
        }

        // The axios interceptor returns the data directly, not the full response
        const responseData = await axiosInstance.post('/userApp-infinity-getCart', requestData) as GetCartsForAdminResponse;
        logger.log('responseData', responseData);
        // Normalize carts to handle null/undefined values
        const normalizedCarts = responseData.carts.map((cart: unknown) => normalizeAdminCart(cart as Record<string, unknown>));

        logger.debug('Cart Service: Successfully fetched carts', {
            count: normalizedCarts.length,
            pagination: {
                currentPage: responseData.currentPageNumber,
                totalPages: responseData.totalNumberOfPages,
                totalRows: responseData.totalNumberOfRows
            }
        });

        return {
            carts: normalizedCarts,
            pagination: {
                currentPage: responseData.currentPageNumber,
                totalPages: responseData.totalNumberOfPages,
                totalRows: responseData.totalNumberOfRows,
                appliedLimit: parseInt(responseData.appliedLimit)
            }
        };
    } catch (error) {
        logger.error('Cart Service: Failed to fetch admin carts', {
            error: error instanceof Error ? error.message : String(error),
            pageNumber,
            limit,
            filters
        });

        // Handle specific error cases
        if (error && typeof error === 'object' && 'message' in error) {
            throw new Error(`Failed to fetch carts: ${(error as Error).message}`);
        } else if (typeof error === 'string') {
            throw new Error(`Failed to fetch carts: ${error}`);
        } else {
            throw new Error('Failed to fetch carts. Please try again.');
        }
    }
};

/**
 * Calculates the total value of an admin cart
 * @param cart Admin cart object
 * @returns Total value as number
 */
export const calculateAdminCartTotal = (cart: AdminCart): number => {
    if (!cart.cartJson?.skuItems || !Array.isArray(cart.cartJson.skuItems)) {
        return 0;
    }

    return cart.cartJson.skuItems.reduce((total, item) => {
        const sellingPrice = item.sellingPrice || 0;
        const quantity = item.quantity || 0;
        return total + (sellingPrice * quantity);
    }, 0);
};

/**
 * Formats admin cart created date for display
 * @param timestamp Epoch milliseconds
 * @returns Formatted date string
 */
export const formatCartCreatedDate = (timestamp: number): string => {
    if (!timestamp || timestamp <= 0) {
        return 'No date';
    }

    try {
        const date = new Date(timestamp);
        if (isNaN(date.getTime())) {
            return 'Invalid date';
        }

        // Format date and time
        return date.toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        console.error('Error formatting cart created date:', error);
        return 'Invalid date';
    }
};

/**
 * Gets unique cart statuses from carts array
 * @param carts Array of admin carts
 * @returns Array of unique cart statuses
 */
export const getUniqueCartStatuses = (carts: AdminCart[]): AdminCartStatus[] => {
    if (!carts || !Array.isArray(carts)) {
        return [];
    }

    const statuses = carts
        .map(cart => cart.status)
        .filter((status): status is AdminCartStatus => status !== null && status !== undefined);

    return Array.from(new Set(statuses)).sort();
};

/**
 * Gets default date range for cart filtering (yesterday + today)
 * @returns Object with dateFrom and dateTo in YYYY-MM-DD format
 */
export const getDefaultCartDateRange = (): { dateFrom: string; dateTo: string } => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    return {
        dateFrom: yesterday.toISOString().split('T')[0],
        dateTo: today.toISOString().split('T')[0]
    };
};

/**
 * Gets cart by ID for admin (placeholder for future implementation)
 * @param cartId Cart ID
 * @returns Promise that resolves with cart details
 */
export const getCartByIdForAdmin = async (cartId: string): Promise<AdminCart> => {
    try {
        console.log(`[cartService] Fetching admin cart by ID: ${cartId}`);

        const requestData = {
            cartId: cartId
        };

        // The axios interceptor returns the data directly, not the full response
        const responseData = await axiosInstance.post('/userApp-infinity-getCart', requestData);

        console.log(`[cartService] Raw API response for cart ${cartId}:`, responseData);

        // The API returns the data wrapped in a structure with carts array
        if (!responseData || typeof responseData !== 'object') {
            throw new Error('Invalid response format from server');
        }

        // Extract the cart from the response structure
        const carts = (responseData as any).carts;
        if (!Array.isArray(carts) || carts.length === 0) {
            throw new Error('Cart not found in response');
        }

        const cartData = carts[0]; // Get the first (and should be only) cart
        console.log(`[cartService] Extracted cart data for ${cartId}:`, cartData);

        // Normalize the cart to handle null/undefined values
        const normalizedCart = normalizeAdminCart(cartData as Record<string, unknown>);

        console.log(`[cartService] Normalized cart for ${cartId}:`, normalizedCart);
        console.log(`[cartService] Successfully fetched cart: ${cartId}`);
        return normalizedCart;
    } catch (error) {
        console.error(`[cartService] Failed to fetch admin cart ${cartId}:`, error);

        // Handle specific error cases
        if (error && typeof error === 'object' && 'message' in error) {
            throw new Error(`Failed to fetch cart: ${(error as Error).message}`);
        } else if (typeof error === 'string') {
            throw new Error(`Failed to fetch cart: ${error}`);
        } else {
            throw new Error('Failed to fetch cart. Please try again.');
        }
    }
};

/**
 * Syncs admin cart changes to backend using existing syncCartToBackend
 * @param cartId Cart ID
 * @param editData Updated cart data
 * @returns Promise that resolves when sync is complete (fire-and-forget)
 */
export const syncAdminCartChanges = async (cartId: string, editData: {
    customerName?: string;
    customerPhone?: string;
    landmark?: string;
    location?: { lat: number; lng: number };
    cartItems?: Array<{
        skuId: number;
        variantSkuId?: number;
        quantity: number;
    }>;
}): Promise<void> => {
    try {
        console.log(`[cartService] Syncing admin cart changes for cart: ${cartId}`);

        // Convert admin cart items to CartItem format
        const cartItems = (editData.cartItems || []).map(item => ({
            skuId: item.skuId,
            variantSkuId: item.variantSkuId,
            quantity: item.quantity
        }));

        // Use existing syncCartToBackend function
        await syncCartToBackend(
            cartId,
            cartItems,
            {
                customerName: editData.customerName,
                customerPhone: editData.customerPhone,
                landmark: editData.landmark,
                location: editData.location
            }
        );

        console.log(`[cartService] Admin cart sync initiated for cart: ${cartId}`);
    } catch (error) {
        console.error(`[cartService] Failed to sync admin cart changes for ${cartId}:`, error);
        throw new Error('Failed to save cart changes. Please try again.');
    }
};

/**
 * Processes admin cart checkout using existing processCheckout
 * @param cartId Cart ID
 * @param checkoutData Checkout data including customer info and cart items
 * @returns Promise that resolves with checkout response
 */
export const processAdminCartCheckout = async (cartId: string, checkoutData: {
    customerName: string;
    customerPhone: string;
    landmark?: string;
    location: { lat: number; lng: number };
    cartItems: Array<{
        skuId: number;
        variantSkuId?: number;
        quantity: number;
    }>;
}): Promise<CheckoutResponse> => {
    try {
        console.log(`[cartService] Processing admin cart checkout for cart: ${cartId}`);

        // Convert admin cart items to CartItem format for processCheckout
        const cartItems = checkoutData.cartItems.map(item => ({
            skuId: item.skuId,
            variantSkuId: item.variantSkuId,
            quantity: item.quantity
        }));

        // Use existing processCheckout function
        const checkoutRequest: InternalCheckoutData = {
            customerName: checkoutData.customerName,
            customerPhone: checkoutData.customerPhone,
            landmark: checkoutData.landmark,
            location: checkoutData.location,
            cartItems: cartItems,
            cartId: cartId
        };

        const response = await processCheckout(checkoutRequest);

        console.log(`[cartService] Admin cart checkout successful for cart: ${cartId}`);
        return response;
    } catch (error) {
        console.error(`[cartService] Failed to process admin cart checkout for ${cartId}:`, error);
        throw new Error('Failed to process checkout. Please try again.');
    }
};

/**
 * Marks a cart as unreachable (UNREACHABLE_1 or UNREACHABLE_2)
 * @param cartId Cart ID to mark as unreachable
 * @param unreachableType Type of unreachable status ('UNREACHABLE_1' or 'UNREACHABLE_2')
 * @returns Promise that resolves with success message
 */
export const markCartUnreachable = async (cartId: string, unreachableType: 'UNREACHABLE_1' | 'UNREACHABLE_2'): Promise<string> => {
    try {
        console.log(`[cartService] Marking cart ${cartId} as ${unreachableType}`);

        // Get current cart data to preserve existing information
        const currentCart = await getCartByIdForAdmin(cartId);

        // Convert cart items to the format needed for sync
        const cartItems = currentCart.cartJson.skuItems.map(item => ({
            skuId: item.skuId,
            variantSkuId: undefined, // Admin carts don't use variants in this structure
            quantity: item.quantity
        }));

        // Parse location from current cart
        const location = {
            lat: parseFloat(currentCart.cartJson.customer.location.lat) || 0,
            lng: parseFloat(currentCart.cartJson.customer.location.lng) || 0
        };

        // Build the sync request with updated status
        const syncRequest: CheckoutRequest = {
            cartJson: {
                skuItems: currentCart.cartJson.skuItems.map(item => ({
                    skuId: item.skuId,
                    quantity: item.quantity,
                    sellingPrice: item.sellingPrice,
                    costPrice: item.costPrice,
                    mrp: item.mrp,
                    skuName: item.skuName || '',
                    skuImage: item.skuImage || ''
                })),
                customer: currentCart.cartJson.customer
            },
            cartId: cartId,
            userType: 'BHUVANESH', // Admin action
            status: unreachableType // Update status to unreachable
        };

        // Make API call to update cart status
        const responseData = await axiosInstance.post(getCheckoutEndpoint(), syncRequest);

        console.log(`[cartService] Successfully marked cart ${cartId} as ${unreachableType}`);

        // Return success message
        if (responseData && typeof responseData === 'object' && (responseData as any).message) {
            return (responseData as any).message;
        }
        return `Cart marked as ${unreachableType} successfully`;
    } catch (error) {
        console.error(`[cartService] Failed to mark cart ${cartId} as ${unreachableType}:`, error);

        if (error && typeof error === 'object' && 'message' in error) {
            throw new Error(`Failed to mark cart as unreachable: ${(error as Error).message}`);
        } else if (typeof error === 'string') {
            throw new Error(`Failed to mark cart as unreachable: ${error}`);
        } else {
            throw new Error('Failed to mark cart as unreachable. Please try again.');
        }
    }
};

/**
 * Validates admin cart items and returns validation results
 * @param cartItems Array of cart items to validate
 * @returns Promise with validation results
 */
export const validateAdminCartItems = async (cartItems: Array<{
    skuId: number;
    variantSkuId?: number;
    quantity: number;
    skuName?: string;
}>): Promise<{
    validItems: Array<{ skuId: number; variantSkuId?: number; quantity: number; skuName: string; sku: SKU }>;
    invalidItems: Array<{ skuId: number; variantSkuId?: number; quantity: number; skuName?: string; reason: string }>;
}> => {
    const validItems: Array<{ skuId: number; variantSkuId?: number; quantity: number; skuName: string; sku: SKU }> = [];
    const invalidItems: Array<{ skuId: number; variantSkuId?: number; quantity: number; skuName?: string; reason: string }> = [];

    for (const item of cartItems) {
        try {
            const sku = await getSkuById(item.skuId);

            if (!sku) {
                invalidItems.push({
                    skuId: item.skuId,
                    variantSkuId: item.variantSkuId,
                    quantity: item.quantity,
                    skuName: item.skuName,
                    reason: 'SKU no longer exists'
                });
                continue;
            }

            // Check variant if specified
            if (item.variantSkuId && sku.variants) {
                const variant = sku.variants.find(v => v.skuId === item.variantSkuId);
                if (!variant) {
                    invalidItems.push({
                        skuId: item.skuId,
                        variantSkuId: item.variantSkuId,
                        quantity: item.quantity,
                        skuName: item.skuName,
                        reason: 'SKU variant no longer exists'
                    });
                    continue;
                }
            }

            validItems.push({
                skuId: item.skuId,
                variantSkuId: item.variantSkuId,
                quantity: item.quantity,
                skuName: sku.name.en,
                sku: sku
            });
        } catch (error) {
            console.error(`Error validating SKU ${item.skuId}:`, error);
            invalidItems.push({
                skuId: item.skuId,
                variantSkuId: item.variantSkuId,
                quantity: item.quantity,
                skuName: item.skuName,
                reason: 'Unable to validate SKU'
            });
        }
    }

    return { validItems, invalidItems };
};
/**
 * tRPC API Route Handler for Next.js App Router
 * 
 * This file handles all tRPC requests in the Next.js App Router,
 * providing the main API endpoint for the discount system and other features.
 */

import { fetchRequestHandler } from '@trpc/server/adapters/fetch';
import { type NextRequest } from 'next/server';
import { appRouter } from '../../../../server/shared/trpc/router';

/**
 * Handle tRPC requests
 */
const handler = (req: NextRequest) =>
  fetchRequestHandler({
    endpoint: '/api/trpc',
    req,
    router: appRouter,
    createContext: () => {
      // Extract user information from request headers
      const userId = req.headers.get('x-user-id') || undefined;
      const userRole = req.headers.get('x-user-role') || undefined;
      const isAdmin = userRole === 'admin' || userRole === 'system-admin';
      const authToken = req.headers.get('authorization') || undefined;



      return {
        req,
        userId,
        userRole,
        isAdmin,
        authToken,
      };
    },
    onError:
      process.env.NODE_ENV === 'development'
        ? ({ path, error }) => {
          // eslint-disable-next-line no-console
          console.error(
            `❌ tRPC failed on ${path ?? '<no-path>'}: ${error.message}`
          );
        }
        : undefined,
  });

export { handler as GET, handler as POST };